# BEGIN WordPress
<IfModule mod_rewrite.c>
RewriteEngine On
RewriteRule .* - [E=HTTP_AUTHORIZATION:%{HTTP:Authorization}]
RewriteBase /
RewriteRule ^index\.php$ - [L]
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d
RewriteRule . /index.php [L]
</IfModule>
# END WordPress

# ===== تحسينات الأداء لثيم عرب استوديو =====

# تفعيل ضغط GZIP
<IfModule mod_deflate.c>
    # ضغط ملفات HTML, CSS, JavaScript, Text, XML والخطوط
    AddOutputFilterByType DEFLATE text/plain
    AddOutputFilterByType DEFLATE text/html
    AddOutputFilterByType DEFLATE text/xml
    AddOutputFilterByType DEFLATE text/css
    AddOutputFilterByType DEFLATE text/javascript
    AddOutputFilterByType DEFLATE application/xml
    AddOutputFilterByType DEFLATE application/xhtml+xml
    AddOutputFilterByType DEFLATE application/rss+xml
    AddOutputFilterByType DEFLATE application/javascript
    AddOutputFilterByType DEFLATE application/x-javascript
    AddOutputFilterByType DEFLATE application/json
    AddOutputFilterByType DEFLATE application/ld+json
    AddOutputFilterByType DEFLATE application/font-woff
    AddOutputFilterByType DEFLATE application/font-woff2
    AddOutputFilterByType DEFLATE font/woff
    AddOutputFilterByType DEFLATE font/woff2
    AddOutputFilterByType DEFLATE image/svg+xml
    
    # إزالة ETags للملفات المضغوطة
    <IfModule mod_headers.c>
        Header append Vary User-Agent env=!dont-vary
    </IfModule>
</IfModule>

# تفعيل التخزين المؤقت للمتصفح
<IfModule mod_expires.c>
    ExpiresActive On
    
    # الصور
    ExpiresByType image/jpg "access plus 1 year"
    ExpiresByType image/jpeg "access plus 1 year"
    ExpiresByType image/gif "access plus 1 year"
    ExpiresByType image/png "access plus 1 year"
    ExpiresByType image/webp "access plus 1 year"
    ExpiresByType image/svg+xml "access plus 1 year"
    ExpiresByType image/x-icon "access plus 1 year"
    
    # الفيديو والصوت
    ExpiresByType video/mp4 "access plus 1 year"
    ExpiresByType video/webm "access plus 1 year"
    ExpiresByType audio/mp3 "access plus 1 year"
    ExpiresByType audio/ogg "access plus 1 year"
    
    # الخطوط
    ExpiresByType font/woff "access plus 1 year"
    ExpiresByType font/woff2 "access plus 1 year"
    ExpiresByType application/font-woff "access plus 1 year"
    ExpiresByType application/font-woff2 "access plus 1 year"
    
    # CSS و JavaScript
    ExpiresByType text/css "access plus 1 month"
    ExpiresByType application/javascript "access plus 1 month"
    ExpiresByType text/javascript "access plus 1 month"
    
    # HTML
    ExpiresByType text/html "access plus 1 hour"
    
    # XML و JSON
    ExpiresByType application/xml "access plus 1 hour"
    ExpiresByType application/json "access plus 1 hour"
    
    # الافتراضي
    ExpiresDefault "access plus 1 month"
</IfModule>

# إعداد Cache-Control Headers
<IfModule mod_headers.c>
    # الصور والخطوط
    <FilesMatch "\.(jpg|jpeg|png|gif|webp|svg|woff|woff2|ttf|eot)$">
        Header set Cache-Control "public, max-age=31536000, immutable"
    </FilesMatch>
    
    # CSS و JavaScript
    <FilesMatch "\.(css|js)$">
        Header set Cache-Control "public, max-age=2592000"
    </FilesMatch>
    
    # HTML
    <FilesMatch "\.(html|htm)$">
        Header set Cache-Control "public, max-age=3600"
    </FilesMatch>
    
    # إزالة ETags
    Header unset ETag
    FileETag None
    
    # تحسين الأمان
    Header always set X-Content-Type-Options nosniff
    Header always set X-Frame-Options DENY
    Header always set X-XSS-Protection "1; mode=block"
    Header always set Referrer-Policy "strict-origin-when-cross-origin"
    Header always set Permissions-Policy "geolocation=(), microphone=(), camera=()"
    
    # تحسين الأداء
    Header set Connection keep-alive
</IfModule>

# تحسين الأمان
<IfModule mod_rewrite.c>
    # منع الوصول للملفات الحساسة
    RewriteRule ^wp-config\.php$ - [F,L]
    RewriteRule ^wp-admin/includes/ - [F,L]
    RewriteRule ^wp-includes/[^/]+\.php$ - [F,L]
    RewriteRule ^wp-includes/js/tinymce/langs/.+\.php - [F,L]
    RewriteRule ^wp-includes/theme-compat/ - [F,L]
    
    # منع الوصول للملفات المخفية
    RewriteRule (^\.|/\.) - [F]
    
    # حماية من SQL Injection
    RewriteCond %{QUERY_STRING} (\<|%3C).*script.*(\>|%3E) [NC,OR]
    RewriteCond %{QUERY_STRING} GLOBALS(=|\[|\%[0-9A-Z]{0,2}) [OR]
    RewriteCond %{QUERY_STRING} _REQUEST(=|\[|\%[0-9A-Z]{0,2}) [OR]
    RewriteCond %{QUERY_STRING} (\<|%3C).*iframe.*(\>|%3E) [NC,OR]
    RewriteCond %{QUERY_STRING} (\<|%3C).*object.*(\>|%3E) [NC,OR]
    RewriteCond %{QUERY_STRING} (\<|%3C).*embed.*(\>|%3E) [NC,OR]
    RewriteCond %{QUERY_STRING} base64_encode.*\(.*\) [NC,OR]
    RewriteCond %{QUERY_STRING} base64_(en|de)code[^(]*\([^)]*\) [NC,OR]
    RewriteCond %{QUERY_STRING} (\\x00|\\x04|\\x08|\\x0d|\\x1b|\\x20|\\x3c|\\x3e|\\x7f) [NC]
    RewriteRule ^(.*)$ - [F,L]
</IfModule>

# تحسين سرعة التحميل
<IfModule mod_mime.c>
    # تحديد أنواع MIME للخطوط
    AddType application/font-woff woff
    AddType application/font-woff2 woff2
    AddType application/vnd.ms-fontobject eot
    AddType application/x-font-ttf ttf
    AddType font/opentype otf
    
    # تحديد أنواع MIME للصور الحديثة
    AddType image/webp webp
    AddType image/avif avif
    
    # تحديد أنواع MIME للملفات الأخرى
    AddType application/javascript js
    AddType text/css css
</IfModule>

# تحسين الاتصال
<IfModule mod_headers.c>
    # تفعيل HTTP/2 Server Push للموارد المهمة
    <FilesMatch "\.html$">
        Header add Link "</wp-content/themes/arabstudio/style.css>; rel=preload; as=style"
        Header add Link "</wp-content/themes/arabstudio/js/main.js>; rel=preload; as=script"
        Header add Link "<https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700;800;900&display=swap>; rel=preload; as=style"
    </FilesMatch>
</IfModule>

# منع hotlinking للصور
<IfModule mod_rewrite.c>
    RewriteCond %{HTTP_REFERER} !^$
    RewriteCond %{HTTP_REFERER} !^https?://(www\.)?arabstudio\.com [NC]
    RewriteCond %{HTTP_REFERER} !^https?://(www\.)?google\. [NC]
    RewriteCond %{HTTP_REFERER} !^https?://(www\.)?bing\. [NC]
    RewriteCond %{HTTP_REFERER} !^https?://(www\.)?yahoo\. [NC]
    RewriteRule \.(jpg|jpeg|png|gif|webp|svg)$ - [F,L]
</IfModule>

# تحسين الفهرسة لمحركات البحث
<IfModule mod_rewrite.c>
    # إعادة توجيه WWW
    RewriteCond %{HTTP_HOST} ^www\.(.*)$ [NC]
    RewriteRule ^(.*)$ https://%1/$1 [R=301,L]
    
    # إعادة توجيه HTTP إلى HTTPS
    RewriteCond %{HTTPS} off
    RewriteRule ^(.*)$ https://%{HTTP_HOST}%{REQUEST_URI} [L,R=301]
    
    # إزالة الشرطة المائلة الزائدة
    RewriteCond %{REQUEST_FILENAME} !-d
    RewriteCond %{REQUEST_URI} (.+)/$
    RewriteRule ^(.+)/$ /$1 [R=301,L]
</IfModule>

# تحسين الذاكرة
<IfModule mod_php.c>
    php_value memory_limit 256M
    php_value max_execution_time 300
    php_value max_input_vars 3000
    php_value post_max_size 32M
    php_value upload_max_filesize 32M
</IfModule>

# منع عرض محتويات المجلدات
Options -Indexes

# حماية ملف .htaccess
<Files .htaccess>
    Order allow,deny
    Deny from all
</Files>

# حماية ملفات النسخ الاحتياطي
<FilesMatch "\.(bak|backup|old|orig|original|tmp|temp)$">
    Order allow,deny
    Deny from all
</FilesMatch>

# تحسين الأداء للأجهزة المحمولة
<IfModule mod_headers.c>
    # تحسين التحميل للأجهزة المحمولة
    SetEnvIf User-Agent ".*Mobile.*" mobile
    Header set Cache-Control "public, max-age=3600" env=mobile
</IfModule>
