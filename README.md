# عرب استوديو - ثيم WordPress فخم ومتطور

![عرب استوديو](https://img.shields.io/badge/Arab%20Studio-v1.0.0-red?style=for-the-badge&logo=wordpress)
![WordPress](https://img.shields.io/badge/WordPress-6.4+-blue?style=for-the-badge&logo=wordpress)
![PHP](https://img.shields.io/badge/PHP-7.4+-purple?style=for-the-badge&logo=php)
![License](https://img.shields.io/badge/License-GPL%20v2-green?style=for-the-badge)

## 🎯 نظرة عامة

**عرب استوديو** هو ثيم WordPress فخم ومتطور مصمم خصيصاً لشركات الإعلان والتسويق الرقمي. يتميز بتصميم داكن أنيق مع تأثيرات بصرية متقدمة وأنيميشن ثلاثي الأبعاد باستخدام Three.js.

### 🏢 معلومات الشركة
- **الاسم**: عرب استوديو للإعلان والتسويق الرقمي
- **العنوان**: البصرة - الطويسة مقابل مركز شرطة الرباط
- **الألوان**: أحمر (#dc2626)، رمادي داكن (#1f2937)، أبيض (#ffffff)
- **التخصص**: الإعلان والتسويق الرقمي

## ✨ الميزات الرئيسية

### 🎨 التصميم والواجهة
- ✅ تصميم فخم وداكن مع ألوان احترافية
- ✅ أنيميشن ثلاثي الأبعاد باستخدام Three.js
- ✅ تأثيرات بصرية متقدمة مع GSAP
- ✅ مؤشر ماوس مخصص وتفاعلي
- ✅ تأثيرات الإضاءة والنيون
- ✅ خلفيات متحركة وجسيمات

### 📱 التجاوب والأداء
- ✅ تجاوب كامل مع جميع الأجهزة (الهواتف، الأجهزة اللوحية، أجهزة الكمبيوتر)
- ✅ تحسين الأداء وسرعة التحميل
- ✅ ضغط الملفات وتفعيل GZIP
- ✅ تحميل كسول للصور (Lazy Loading)
- ✅ دعم WebP للصور

### 🌐 اللغة والمحتوى
- ✅ دعم كامل للغة العربية (RTL)
- ✅ خط Cairo من Google Fonts
- ✅ محتوى محسّن لمحركات البحث
- ✅ Schema.org Markup
- ✅ Open Graph و Twitter Cards

### 🔧 التقنيات المستخدمة
- ✅ HTML5 & CSS3 المتقدم
- ✅ JavaScript (ES6+)
- ✅ Three.js للأنيميشن ثلاثي الأبعاد
- ✅ GSAP للأنيميشن المتقدم
- ✅ Font Awesome للأيقونات
- ✅ CSS Grid & Flexbox
- ✅ CSS Variables

## 📁 هيكل الملفات

```
arabstudio/
├── 📄 style.css              # الأنماط الرئيسية
├── 📄 responsive.css         # أنماط التجاوب المتقدم
├── 📄 functions.php          # وظائف الثيم
├── 📄 index.php             # الصفحة الرئيسية
├── 📄 header.php            # رأس الصفحة
├── 📄 footer.php            # تذييل الصفحة
├── 📄 single.php            # صفحة المقال المفرد
├── 📄 page.php              # صفحة عامة
├── 📄 404.php               # صفحة الخطأ 404
├── 📄 readme.txt            # معلومات الثيم
├── 📄 test-responsive.html  # ملف اختبار التجاوب
├── 📁 js/
│   └── 📄 main.js           # الجافاسكريبت الرئيسي
├── 📁 assets/
│   └── 📁 images/           # الصور والأيقونات
└── 📁 languages/            # ملفات الترجمة
```

## 🚀 التثبيت والإعداد

### 1. متطلبات النظام
- WordPress 5.0 أو أحدث
- PHP 7.4 أو أحدث
- MySQL 5.6 أو أحدث
- ذاكرة PHP: 256MB على الأقل

### 2. خطوات التثبيت

1. **تحميل الثيم**:
   ```bash
   # نسخ ملفات الثيم إلى مجلد الثيمات
   cp -r arabstudio/ /path/to/wordpress/wp-content/themes/
   ```

2. **تفعيل الثيم**:
   - اذهب إلى لوحة تحكم WordPress
   - المظهر > الثيمات
   - فعّل ثيم "عرب استوديو"

3. **إعداد wp-config.php**:
   ```php
   // إضافة الثوابت المطلوبة
   define('COMPANY_NAME', 'عرب استوديو');
   define('COMPANY_ADDRESS', 'البصرة - الطويسة مقابل مركز شرطة الرباط');
   define('COMPANY_PHONE', '+964 XXX XXX XXXX');
   define('COMPANY_EMAIL', '<EMAIL>');
   ```

4. **إعداد .htaccess**:
   - انسخ محتوى ملف .htaccess المرفق
   - يحتوي على تحسينات الأداء والأمان

### 3. إعداد القوائم
1. اذهب إلى المظهر > القوائم
2. أنشئ قائمة جديدة باسم "القائمة الرئيسية"
3. أضف الصفحات المطلوبة
4. اختر موقع القائمة: "القائمة الرئيسية"

## 🎨 التخصيص

### الألوان
يمكن تخصيص الألوان من خلال متغيرات CSS في ملف style.css:

```css
:root {
    --primary-red: #dc2626;      /* اللون الأحمر الأساسي */
    --dark-gray: #1f2937;        /* الرمادي الداكن */
    --darker-gray: #111827;      /* الرمادي الأكثر قتامة */
    --light-gray: #6b7280;       /* الرمادي الفاتح */
    --white: #ffffff;            /* الأبيض */
}
```

### الخطوط
الثيم يستخدم خط Cairo من Google Fonts. يمكن تغييره في functions.php:

```php
wp_enqueue_style('google-fonts', 'https://fonts.googleapis.com/css2?family=YourFont&display=swap');
```

### الأنيميشن
يمكن تعديل إعدادات الأنيميشن في ملف js/main.js:

```javascript
// تعديل سرعة الأنيميشن
const animationSpeed = 0.01; // قيمة أقل = أبطأ

// تعديل عدد الجسيمات
const particleCount = 1000; // عدد أقل = أداء أفضل
```

## 📱 اختبار التجاوب

### أدوات الاختبار
1. **ملف الاختبار المدمج**: `test-responsive.html`
2. **أدوات المطور في المتصفح**
3. **مواقع اختبار التجاوب**:
   - [Responsive Design Checker](https://responsivedesignchecker.com/)
   - [Am I Responsive?](http://ami.responsivedesign.is/)

### نقاط الكسر المدعومة
- **XS**: 0-575px (الهواتف الصغيرة)
- **SM**: 576-767px (الهواتف الكبيرة)
- **MD**: 768-991px (الأجهزة اللوحية)
- **LG**: 992-1199px (أجهزة الكمبيوتر الصغيرة)
- **XL**: 1200-1399px (أجهزة الكمبيوتر الكبيرة)
- **XXL**: 1400px+ (الشاشات الكبيرة جداً)

## 🔧 تحسين الأداء

### تحسينات مطبقة
- ✅ ضغط GZIP للملفات
- ✅ تخزين مؤقت للمتصفح
- ✅ تحسين الصور تلقائياً
- ✅ تأجيل تحميل JavaScript
- ✅ تحميل كسول للصور
- ✅ تحسين قاعدة البيانات
- ✅ إزالة الموارد غير المستخدمة

### نصائح إضافية
1. **استخدم CDN** لتسريع تحميل الموارد
2. **فعّل التخزين المؤقت** على مستوى الخادم
3. **حسّن الصور** قبل رفعها
4. **استخدم WebP** للصور الحديثة

## 🛡️ الأمان

### تحسينات الأمان المطبقة
- ✅ منع الوصول للملفات الحساسة
- ✅ حماية من SQL Injection
- ✅ منع تعداد المستخدمين
- ✅ إضافة Security Headers
- ✅ منع hotlinking للصور
- ✅ تشفير المعلومات الحساسة

## 📊 تحسين محركات البحث (SEO)

### ميزات SEO المدمجة
- ✅ Schema.org Markup
- ✅ Open Graph Tags
- ✅ Twitter Cards
- ✅ Sitemap.xml محسّن
- ✅ Robots.txt محسّن
- ✅ Meta Tags محسّنة
- ✅ بنية URL صديقة لمحركات البحث

## 🌐 الدعم والمساعدة

### معلومات التواصل
- **البريد الإلكتروني**: <EMAIL>
- **الهاتف**: +964 XXX XXX XXXX
- **العنوان**: البصرة - الطويسة مقابل مركز شرطة الرباط
- **الموقع**: https://arabstudio.com

### الدعم الفني
- **التوثيق**: متوفر في ملف readme.txt
- **الأسئلة الشائعة**: متوفرة في الموقع
- **الدعم المباشر**: عبر البريد الإلكتروني أو الهاتف

## 📝 الترخيص

هذا الثيم مرخص تحت **رخصة GPL v2 أو أحدث**. يمكنك استخدامه وتعديله بحرية وفقاً لشروط الرخصة.

## 🔄 التحديثات

### الإصدار الحالي: v1.0.0
- ✅ الإصدار الأول مع جميع الميزات الأساسية
- ✅ تصميم فخم وداكن
- ✅ أنيميشن ثلاثي الأبعاد
- ✅ تجاوب كامل
- ✅ تحسين الأداء والأمان

### التحديثات المستقبلية
- 🔄 إضافة المزيد من القوالب
- 🔄 تحسينات الأداء الإضافية
- 🔄 ميزات جديدة للأنيميشن
- 🔄 دعم المزيد من اللغات

## 🙏 شكر وتقدير

تم تطوير هذا الثيم بواسطة فريق **عرب استوديو** للإعلان والتسويق الرقمي. نشكر جميع المساهمين والمطورين الذين ساعدوا في إنجاز هذا المشروع.

### المكتبات المستخدمة
- [Three.js](https://threejs.org/) - للأنيميشن ثلاثي الأبعاد
- [GSAP](https://greensock.com/gsap/) - للأنيميشن المتقدم
- [Font Awesome](https://fontawesome.com/) - للأيقونات
- [Google Fonts](https://fonts.google.com/) - للخطوط

---

**© 2025 عرب استوديو. جميع الحقوق محفوظة.**
