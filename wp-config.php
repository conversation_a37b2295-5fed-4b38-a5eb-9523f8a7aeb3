<?php
/**
 * إعدادات WordPress لموقع عرب استوديو
 * Arab Studio - WordPress Configuration
 *
 * هذا الملف يحتوي على الإعدادات التالية:
 * * إعدادات قاعدة البيانات
 * * المفاتيح السرية
 * * بادئة جداول قاعدة البيانات
 * * ABSPATH
 *
 * @link https://developer.wordpress.org/advanced-administration/wordpress/wp-config/
 * @package WordPress
 */

// ** إعدادات قاعدة البيانات - يمكنك الحصول على هذه المعلومات من مزود الاستضافة ** //
/** اسم قاعدة البيانات لـ WordPress */
define( 'DB_NAME', 'arabstudio_db' );

/** اسم مستخدم قاعدة البيانات */
define( 'DB_USER', 'arabstudio_user' );

/** كلمة مرور قاعدة البيانات */
define( 'DB_PASSWORD', 'your_secure_password_here' );

/** خادم قاعدة البيانات */
define( 'DB_HOST', 'localhost' );

/** ترميز قاعدة البيانات المستخدم في إنشاء جداول قاعدة البيانات. */
define( 'DB_CHARSET', 'utf8mb4' );

/** نوع ترتيب قاعدة البيانات. لا تغير هذا إن كنت في شك. */
define( 'DB_COLLATE', '' );

/**#@+
 * مفاتيح المصادقة الفريدة وأملاح التشفير.
 *
 * غيّر هذه إلى عبارات فريدة مختلفة! يمكنك توليد هذه باستخدام
 * {@link https://api.wordpress.org/secret-key/1.1/salt/ خدمة مفاتيح WordPress.org السرية}.
 *
 * يمكنك تغيير هذه في أي وقت لإبطال جميع ملفات تعريف الارتباط الموجودة.
 * هذا سيجبر جميع المستخدمين على تسجيل الدخول مرة أخرى.
 *
 * @since 2.6.0
 */
define( 'AUTH_KEY',         'ضع مفتاحك الفريد هنا' );
define( 'SECURE_AUTH_KEY',  'ضع مفتاحك الفريد هنا' );
define( 'LOGGED_IN_KEY',    'ضع مفتاحك الفريد هنا' );
define( 'NONCE_KEY',        'ضع مفتاحك الفريد هنا' );
define( 'AUTH_SALT',        'ضع ملحك الفريد هنا' );
define( 'SECURE_AUTH_SALT', 'ضع ملحك الفريد هنا' );
define( 'LOGGED_IN_SALT',   'ضع ملحك الفريد هنا' );
define( 'NONCE_SALT',       'ضع ملحك الفريد هنا' );

/**#@-*/

/**
 * بادئة جداول قاعدة البيانات لـ WordPress.
 *
 * يمكنك الحصول على تثبيتات متعددة في قاعدة بيانات واحدة إذا أعطيت لكل منها
 * بادئة فريدة. أرقام وحروف وشرطات سفلية فقط من فضلك!
 */
$table_prefix = 'as_';

/**
 * للمطورين: وضع تصحيح الأخطاء في WordPress.
 *
 * غيّر هذا إلى true لتمكين عرض الإشعارات أثناء التطوير.
 * يُنصح بشدة أن يستخدم مطورو الإضافات والقوالب WP_DEBUG
 * في بيئات التطوير الخاصة بهم.
 *
 * للحصول على معلومات حول الثوابت الأخرى التي يمكن استخدامها للتصحيح،
 * قم بزيارة الدليل.
 *
 * @link https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/
 */
define( 'WP_DEBUG', false );
define( 'WP_DEBUG_LOG', false );
define( 'WP_DEBUG_DISPLAY', false );

// ===== إعدادات الأداء والأمان لعرب استوديو =====

/** تحسين الأداء */
define( 'WP_CACHE', true );
define( 'COMPRESS_CSS', true );
define( 'COMPRESS_SCRIPTS', true );
define( 'CONCATENATE_SCRIPTS', true );
define( 'ENFORCE_GZIP', true );

/** تحسين قاعدة البيانات */
define( 'WP_AUTO_UPDATE_CORE', true );
define( 'AUTOMATIC_UPDATER_DISABLED', false );
define( 'WP_POST_REVISIONS', 5 );
define( 'AUTOSAVE_INTERVAL', 300 );
define( 'EMPTY_TRASH_DAYS', 30 );

/** تحسين الذاكرة */
define( 'WP_MEMORY_LIMIT', '256M' );
define( 'WP_MAX_MEMORY_LIMIT', '512M' );

/** تحسين الأمان */
define( 'DISALLOW_FILE_EDIT', true );
define( 'DISALLOW_FILE_MODS', false );
define( 'FORCE_SSL_ADMIN', true );
define( 'WP_HTTP_BLOCK_EXTERNAL', false );

/** إعدادات الجلسة */
define( 'COOKIE_DOMAIN', '.arabstudio.com' );
define( 'WP_SITEURL', 'https://arabstudio.com' );
define( 'WP_HOME', 'https://arabstudio.com' );

/** إعدادات FTP (إذا لزم الأمر) */
// define( 'FS_METHOD', 'direct' );
// define( 'FTP_HOST', 'ftp.arabstudio.com' );
// define( 'FTP_USER', 'your_ftp_username' );
// define( 'FTP_PASS', 'your_ftp_password' );

/** إعدادات CDN */
define( 'WP_CONTENT_URL', 'https://cdn.arabstudio.com/wp-content' );

/** إعدادات التحميل */
define( 'UPLOADS', 'wp-content/uploads' );

/** إعدادات اللغة */
define( 'WPLANG', 'ar' );

/** إعدادات المنطقة الزمنية */
define( 'WP_DEFAULT_TIMEZONE', 'Asia/Baghdad' );

/** تحسين cron */
define( 'DISABLE_WP_CRON', false );
define( 'WP_CRON_LOCK_TIMEOUT', 60 );

/** إعدادات الإصلاح التلقائي لقاعدة البيانات */
define( 'WP_ALLOW_REPAIR', false );

/** إعدادات الملفات المتعددة */
define( 'WP_ALLOW_MULTISITE', false );

/** إعدادات البريد الإلكتروني */
define( 'WPMS_ON', true );
define( 'WPMS_SMTP_PASS', 'your_email_password' );

/** إعدادات Redis للتخزين المؤقت (إذا كان متاحاً) */
define( 'WP_REDIS_HOST', '127.0.0.1' );
define( 'WP_REDIS_PORT', 6379 );
define( 'WP_REDIS_TIMEOUT', 1 );
define( 'WP_REDIS_READ_TIMEOUT', 1 );
define( 'WP_REDIS_DATABASE', 0 );

/** إعدادات Memcached (إذا كان متاحاً) */
define( 'WP_CACHE_KEY_SALT', 'arabstudio_' );

/** تحسين الاستعلامات */
define( 'WP_USE_EXT_MYSQL', false );

/** إعدادات خاصة بالثيم */
define( 'ARABSTUDIO_VERSION', '1.0.0' );
define( 'ARABSTUDIO_THEME_URL', get_template_directory_uri() );

/** إعدادات API Keys (يجب تشفيرها في الإنتاج) */
// define( 'GOOGLE_MAPS_API_KEY', 'your_google_maps_api_key' );
// define( 'FACEBOOK_APP_ID', 'your_facebook_app_id' );
// define( 'TWITTER_API_KEY', 'your_twitter_api_key' );

/** إعدادات التحليلات */
// define( 'GOOGLE_ANALYTICS_ID', 'GA_MEASUREMENT_ID' );

/** إعدادات النسخ الاحتياطي */
define( 'BACKUP_PATH', ABSPATH . 'backups/' );

/** إعدادات السجلات */
define( 'WP_DEBUG_LOG', true );
define( 'LOG_ERRORS', true );
define( 'ERROR_LOG', ABSPATH . 'wp-content/debug.log' );

/** إعدادات الأمان المتقدمة */
define( 'SECURE_AUTH_COOKIE', true );
define( 'WP_HTTP_BLOCK_EXTERNAL', false );
define( 'WP_ACCESSIBLE_HOSTS', 'api.wordpress.org,*.github.com,*.googleapis.com,*.google.com,*.facebook.com,*.twitter.com,cdnjs.cloudflare.com' );

/** منع تعداد المستخدمين */
if ( ! defined( 'NOBLOGREDIRECT' ) ) {
    define( 'NOBLOGREDIRECT', 'https://arabstudio.com/' );
}

/* هذا كل شيء، توقف عن التحرير! استمتع بالتدوين. */

/** المسار المطلق لمجلد WordPress. */
if ( ! defined( 'ABSPATH' ) ) {
    define( 'ABSPATH', __DIR__ . '/' );
}

/** إعداد متغيرات WordPress وتضمين الملفات. */
require_once ABSPATH . 'wp-settings.php';

/** إعدادات إضافية لتحسين الأداء */
if ( ! defined( 'WP_CLI' ) ) {
    // تحسين استعلامات قاعدة البيانات
    if ( ! defined( 'WP_USE_EXT_MYSQL' ) ) {
        define( 'WP_USE_EXT_MYSQL', false );
    }
    
    // تحسين الذاكرة للعمليات الكبيرة
    ini_set( 'memory_limit', '256M' );
    ini_set( 'max_execution_time', 300 );
    ini_set( 'max_input_vars', 3000 );
}

/** معلومات الشركة للاستخدام في الثيم */
define( 'COMPANY_NAME', 'عرب استوديو' );
define( 'COMPANY_NAME_EN', 'Arab Studio' );
define( 'COMPANY_ADDRESS', 'البصرة - الطويسة مقابل مركز شرطة الرباط' );
define( 'COMPANY_PHONE', '+964 XXX XXX XXXX' );
define( 'COMPANY_EMAIL', '<EMAIL>' );
define( 'COMPANY_WEBSITE', 'https://arabstudio.com' );

/** إعدادات وسائل التواصل الاجتماعي */
define( 'SOCIAL_FACEBOOK', 'https://www.facebook.com/arabstudio' );
define( 'SOCIAL_INSTAGRAM', 'https://www.instagram.com/arabstudio' );
define( 'SOCIAL_LINKEDIN', 'https://www.linkedin.com/company/arabstudio' );
define( 'SOCIAL_TWITTER', 'https://www.twitter.com/arabstudio' );
define( 'SOCIAL_YOUTUBE', 'https://www.youtube.com/arabstudio' );
define( 'SOCIAL_WHATSAPP', 'https://wa.me/964XXXXXXXXX' );
?>
