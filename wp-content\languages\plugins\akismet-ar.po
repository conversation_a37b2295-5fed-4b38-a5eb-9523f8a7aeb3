# Translation of Plugins - Akismet Anti-spam: Spam Protection - Stable (latest release) in Arabic
# This file is distributed under the same license as the Plugins - Akismet Anti-spam: Spam Protection - Stable (latest release) package.
msgid ""
msgstr ""
"PO-Revision-Date: 2025-07-15 18:35:53+0000\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=6; plural=(n == 0) ? 0 : ((n == 1) ? 1 : ((n == 2) ? 2 : ((n % 100 >= 3 && n % 100 <= 10) ? 3 : ((n % 100 >= 11 && n % 100 <= 99) ? 4 : 5))));\n"
"X-Generator: GlotPress/4.0.1\n"
"Language: ar\n"
"Project-Id-Version: Plugins - Akismet Anti-spam: Spam Protection - Stable (latest release)\n"

#: class.akismet-admin.php:700
msgid "<PERSON><PERSON><PERSON> provisionally cleared this comment."
msgstr "مسح أكيسميت هذا التعليق مؤقتًا."

#: class-akismet-compatible-plugins.php:86
msgid "Error getting compatible plugins."
msgstr "خطأ في الحصول على إضافات متوافقة."

#: views/notice.php:70
msgid "Upgrade plan"
msgstr "ترقية الخطة"

#. translators: The placeholder is a URL to the contact form.
#: views/notice.php:64
msgid "If you believe your site should not be classified as commercial, <a href=\"%s\">please get in touch</a>."
msgstr "إذا كنت تعتقد أن موقعك ينبغي عدم تصنيفه على أنه تجاري، <a href=\"%s\">فيرجى التواصل معنا</a>."

#. translators: The placeholder is a URL.
#: views/notice.php:58
msgid "Your current subscription is for <a href=\"%s\">personal, non-commercial use</a>. Please upgrade your plan to continue using Akismet."
msgstr "اشتراكك الحالي مخصص <a href=\"%s\">للاستخدام الشخصي غير التجاري</a>. يرجى ترقية خطتك للاستمرار في استخدام أكيسميت."

#: views/notice.php:54
msgid "We detected commercial activity on your site"
msgstr "اكتشفنا نشاطًا تجاريًا على موقعك"

#: views/notice.php:27
msgid "Almost done! Configure Akismet and say goodbye to spam"
msgstr "على وشك الانتهاء! تكوين أكيسميت وقول وداعًا للبريد المزعج"

#: class.akismet-admin.php:764
msgid "This comment was not sent to Akismet when it was submitted because it was caught by the comment disallowed list."
msgstr "لم يتم إرسال هذا التعليق إلى أكيسميت عند تقديمه لأنه تم اكتشافه في قائمة التعليقات غير المسموح بها."

#: class.akismet-admin.php:761
msgid "This comment was not sent to Akismet when it was submitted because it was caught by something else."
msgstr "لم يتم إرسال هذا التعليق إلى أكيسميت عند تقديمه لأنه تم اكتشافه بواسطة شيء آخر."

#. translators: the placeholder is the URL to the Akismet pricing page.
#: views/notice.php:180
msgid "Please <a href=\"%s\" target=\"_blank\">choose a plan</a> to get started with Akismet."
msgstr "يرجى <a href=\"%s\" target=\"_blank\">اختيار خطة</a> لبدء أكيسميت."

#: views/notice.php:176
msgid "Your API key must have an Akismet plan before it can protect your site from spam."
msgstr "يجب أن يحتوي مفتاح واجهة برمجة التطبيقات لديك على خطة أكيسميت قبل التمكن من حماية موقعك من البريد المزعج."

#: class.akismet-rest-api.php:509
msgid "Multiple comments matched request."
msgstr "تعليقات متعددة مطابقة للطلب."

#: class.akismet-rest-api.php:499
msgid "Could not find matching comment."
msgstr "تعذر العثور على تعليق متطابق."

#: class.akismet-rest-api.php:457
msgid "The 'comments' parameter must be an array."
msgstr "يجب أن يكون معيار \"التعليقات\" مصفوفة."

#: class.akismet-admin.php:758
msgid "Akismet cleared this comment during a recheck. It did not update the comment status because it had already been modified by another user or plugin."
msgstr "مسح أكيسميت هذا التعليق في أثناء إعادة التحقق. لم يتم تحديث حالة التعليق؛ لأنه تم تعديلها بالفعل بواسطة مستخدم آخر أو إضافة أخرى."

#: class.akismet-admin.php:755
msgid "Akismet determined this comment was spam during a recheck. It did not update the comment status because it had already been modified by another user or plugin."
msgstr "أشار أكيسميت إلى أن هذا التعليق كان مزعجًا في أثناء إعادة التحقق. لم يتم تحديث حالة التعليق؛ لأنه تم تعديلها بالفعل بواسطة مستخدم آخر أو إضافة أخرى."

#: class.akismet-admin.php:752
msgid "Akismet cleared this comment and updated its status via webhook."
msgstr "قام أكيسميت بمسح هذا التعليق وتحديث حالته عبر موضع الإضافة على الويب."

#: class.akismet-admin.php:749
msgid "Akismet caught this comment as spam and updated its status via webhook."
msgstr "كان أكيسميت ينظر إلى هذا التعليق على أنه بريد مزعج، بل وقام بتحديث حالته عبر موضع الإضافة على الويب."

#: views/notice.php:198
msgid "Akismet is now protecting your site from spam."
msgstr "تعمل ميزة أكيسميت الآن على حماية موقعك من البريد المزعج."

#: views/config.php:304
msgid "Account overview"
msgstr "نظرة عامة على الحساب"

#. translators: %1$s: spam folder link, %2$d: delete interval in days
#: views/config.php:192
msgid "Spam in the %1$s older than %2$d day is deleted automatically."
msgid_plural "Spam in the %1$s older than %2$d days is deleted automatically."
msgstr[0] "تُحذف رسائل البريد المزعج الموجودة في ⁦%1$s⁩ الأقدم من ⁦%2$d⁩ من الأيام تلقائيًا."
msgstr[1] "تُحذف رسائل البريد المزعج الموجودة في ⁦%1$s⁩ الأقدم من ⁦%2$d⁩ من الأيام تلقائيًا."
msgstr[2] "تُحذف رسائل البريد المزعج الموجودة في ⁦%1$s⁩ الأقدم من ⁦%2$d⁩ من الأيام تلقائيًا."
msgstr[3] "تُحذف رسائل البريد المزعج الموجودة في ⁦%1$s⁩ الأقدم من ⁦%2$d⁩ من الأيام تلقائيًا."
msgstr[4] "تُحذف رسائل البريد المزعج الموجودة في ⁦%1$s⁩ الأقدم من ⁦%2$d⁩ من الأيام تلقائيًا."
msgstr[5] "تُحذف رسائل البريد المزعج الموجودة في ⁦%1$s⁩ الأقدم من ⁦%2$d⁩ من الأيام تلقائيًا."

#: views/config.php:187
msgid "spam folder"
msgstr "مجلد رسائل البريد المزعج"

#: views/config.php:268
msgid "Subscription type"
msgstr "نوع الاشتراك"

#: views/config.php:232
msgid "To help your site with transparency under privacy laws like the GDPR, Akismet can display a notice to your users under your comment forms."
msgstr "لمساعدة موقعك بشأن الشفافية بموجب قوانين الخصوصية، مثل اللائحة العامة لحماية البيانات (GDPR)، يمكن لأكيسميت عرض إشعار لمستخدميك أسفل نماذج التعليق الخاصة بك."

#: views/config.php:154
msgid "Spam filtering"
msgstr "تصفية البريد المزعج"

#: views/config.php:94 views/enter.php:9
msgid "API key"
msgstr "مفتاح الـ API"

#: views/config.php:44
msgid "Akismet stats"
msgstr "إحصائيات  أكيسميت"

#: views/notice.php:47
msgid "WP-Cron has been disabled using the DISABLE_WP_CRON constant. Comment rechecks may not work properly."
msgstr "تم تعطيل WP-Cron باستخدام ثابت DISABLE_WP_CRON. قد لا تعمل عمليات إعادة التحقق من التعليقات بشكل صحيح."

#. translators: %1$s is a human-readable time difference, like "3 hours ago",
#. and %2$s is an already-translated phrase describing how a comment's status
#. changed, like "This comment was reported as spam."
#: class.akismet-admin.php:796
msgid "%1$s - %2$s"
msgstr "%1$s - %2$s"

#: views/get.php:17
msgid "(opens in a new tab)"
msgstr "(تفتح في علامة تبويب جديدة)"

#. translators: The placeholder is the name of a subscription level, like
#. "Plus" or "Enterprise" .
#: views/notice.php:341
msgid "Upgrade to %s"
msgstr "الترقية إلى %s"

#: views/notice.php:336
msgid "Upgrade your subscription level"
msgstr "ترقية مستوى الاشتراك الخاص بك"

#: views/notice.php:293 views/notice.php:301 views/notice.php:309
#: views/notice.php:318
msgid "Learn more about usage limits."
msgstr "معرفة المزيد حول حدود الاستخدام."

#. translators: The first placeholder is a date, the second is a (formatted)
#. number, the third is another formatted number.
#: views/notice.php:285
msgid "Since %1$s, your account made %2$s API calls, compared to your plan&#8217;s limit of %3$s."
msgstr "منذ %1$s أجرى حسابك %2$s استدعاء API (واجهة برمجة التطبيقات)، مقارنة بحدّ خطتك البالغ %3$s."

#: views/notice.php:315
msgid "Your Akismet usage has been over your plan&#8217;s limit for three consecutive months. We have restricted your account for the rest of the month. Upgrade your plan so Akismet can continue blocking spam."
msgstr "تجاوز استخدامك لـ Akismet حدّ خطتك لمدة ثلاثة أشهر متتالية. لقد قيّدنا حسابك لبقية الشهر. قم بترقية خطتك حتى يتمكّن Akismet من مواصلة حظر الرسائل المزعجة."

#: views/notice.php:306
msgid "Your Akismet usage is nearing your plan&#8217;s limit for the third consecutive month. We will restrict your account after you reach the limit. Upgrade your plan so Akismet can continue blocking spam."
msgstr "اقترب استخدامك لـ Akismet من حدّ خطتك للشهر الثالث على التوالي. سنقيّد حسابك بعد أن تصل إلى الحدّ الأقصى. قم بترقية خطتك حتى يتمكّن Akismet من مواصلة حظر الرسائل المزعجة."

#: views/notice.php:298
msgid "Your Akismet usage has been over your plan&#8217;s limit for two consecutive months. Next month, we will restrict your account after you reach the limit. Please consider upgrading your plan."
msgstr "تجاوز استخدامك لـ Akismet حدّ خطتك لمدة شهرين متتاليين. في الشهر المقبل، سنقيّد حسابك بعد أن تصل إلى الحدّ الأقصى. يرجى النظر في ترقية خطتك."

#: views/notice.php:272
msgid "Your account has been restricted"
msgstr "تم تقييد حسابك"

#: views/notice.php:268
msgid "Your Akismet account usage is approaching your plan&#8217;s limit"
msgstr "اقترب استخدام حساب Akismet الخاص بك من حدّ خطتك"

#: views/notice.php:265
msgid "Your Akismet account usage is over your plan&#8217;s limit"
msgstr "تجاوز استخدام حساب Akismet الخاص بك حدّ خطتك"

#. translators: The placeholder is a URL to the Akismet contact form.
#: views/notice.php:228
msgid "Please enter a new key or <a href=\"%s\" target=\"_blank\">contact Akismet support</a>."
msgstr "يرجى إدخال مفتاح جديد أو <a href=\"%s\" target=\"_blank\">الاتصال بفريق دعم Akismet</a>."

#: views/notice.php:222
msgid "Your API key is no longer valid."
msgstr "مفتاح API الخاص بك لم يعد صالحاً."

#. translators: The placeholder is for showing how much of the process has
#. completed, as a percent. e.g., "Checking for Spam (40%)"
#: class.akismet-admin.php:481
msgid "Checking for Spam (%1$s%)"
msgstr "التحقّق من المحتويات المزعجة (%1$s%)"

#: class.akismet-admin.php:812
msgid "No comment history."
msgstr "لا يوجد سجّل للتعليق."

#: class.akismet-admin.php:745
msgid "Akismet was unable to recheck this comment."
msgstr "لم تتمكّن Akismet من التحقّق من هذا التعليق."

#: class.akismet-admin.php:737
msgid "Akismet was unable to check this comment but will automatically retry later."
msgstr "لم تتمكّن Akismet من التحقّق من هذا التعليق ولكن سيعيد المحاولة تلقائياً في وقت لاحق."

#. translators: The placeholder is a WordPress PHP function name.
#: class.akismet-admin.php:706
msgid "Comment was caught by %s."
msgstr "ضُبط التعليق بواسطة %s."

#: class.akismet.php:956
msgid "Akismet is not configured. Please enter an API key."
msgstr "لم يتم تكوين إعدادات إضافة Akismet. يرجى إدخال مفتاح API."

#: views/enter.php:7
msgid "Enter your API key"
msgstr "أدخل مفتاح API الخاص بك"

#: views/connect-jp.php:92
msgid "Set up a different account"
msgstr "إعداد حساب مختلف"

#: views/setup.php:2
msgid "Set up your Akismet account to enable spam filtering on this site."
msgstr "إعداد حساب Akismet الخاص بك لتمكين تصفية الرسائل والتعليقات المزعجة على هذا الموقع."

#: class.akismet-admin.php:1335
msgid "Akismet could not recheck your comments for spam."
msgstr "تعذّر على Akismet إعادة فحص تعليقاتك بحثًا عن الرسائل المزعجة."

#: class.akismet-cli.php:167
msgid "Stats response could not be decoded."
msgstr "استجابة الإحصائيات لا يمكن فك ترميزها."

#: class.akismet-cli.php:161
msgid "Currently unable to fetch stats. Please try again."
msgstr "لا يمكن جلب الإحصائيات حاليًا. يرجى المحاولة مرة أخرى."

#: class.akismet-cli.php:135
msgid "API key must be set to fetch stats."
msgstr "لابدّ من ضبط مفتاح واجهة برمجة التطبيقات (API) لجلب الإحصائيات."

#: views/config.php:225
msgid "Do not display privacy notice."
msgstr "عدم عرض إشعار الخصوصية."

#: views/config.php:217
msgid "Display a privacy notice under your comment forms."
msgstr "عرض إشعار الخصوصية أسفل نماذج التعليق."

#: views/config.php:211
msgid "Akismet privacy notice"
msgstr "إشعار الخصوصية لـ Akismet"

#: views/config.php:206
msgid "Privacy"
msgstr "الخصوصية"

#. translators: %s: Akismet privacy URL
#: class.akismet.php:2085
msgid "This site uses Akismet to reduce spam. <a href=\"%s\" target=\"_blank\" rel=\"nofollow noopener\">Learn how your comment data is processed.</a>"
msgstr "هذا الموقع يستخدم خدمة أكيسميت للتقليل من البريد المزعجة. <a href=\"%s\" target=\"_blank\" rel=\"nofollow noopener\">اعرف المزيد عن كيفية التعامل مع بيانات التعليقات الخاصة بك processed</a>."

#: class.akismet-admin.php:108
msgid "We collect information about visitors who comment on Sites that use our Akismet Anti-spam service. The information we collect depends on how the User sets up Akismet for the Site, but typically includes the commenter's IP address, user agent, referrer, and Site URL (along with other information directly provided by the commenter such as their name, username, email address, and the comment itself)."
msgstr "نجمع معلومات حول الزائرين الذين يعلِّقون على المواقع التي تستخدم خدمة Akismet Anti-spam لدينا. تعتمد المعلومات التي نجمعها على كيفية إعداد المستخدم أكيسميت للموقع، لكنها تتضمن عادة عنوان IP للمعلِّق ووكيل المستخدم والمحيل وعنوان URL للموقع (إلى جانب المعلومات الأخرى التي يقدِّمها المعلِّق مباشرةً، مثل: اسمه واسم المستخدم الخاص به وعنوان بريده الإلكتروني وتعليقه ذاته)."

#: class.akismet.php:467
msgid "Comment discarded."
msgstr "تم تجاهل التعليق."

#: class.akismet-rest-api.php:206
msgid "This site's API key is hardcoded and cannot be deleted."
msgstr "مفتاح واجهة برمجة التطبيق (API) الخاص بهذا الموقع مثبّت ولايمكن حذفه."

#: class.akismet-rest-api.php:190
msgid "The value provided is not a valid and registered API key."
msgstr "القيمة التي تم تقديمها ليست مفتاح API صالحا ومسجلا."

#: class.akismet-rest-api.php:184
msgid "This site's API key is hardcoded and cannot be changed via the API."
msgstr "مفتاح واجهة برمجة التطبيق (API) الخاص بهذا الموقع مثبّت ولايمكن تغييره عبر واجهة البرمجة."

#: class.akismet-rest-api.php:84 class.akismet-rest-api.php:97
msgid "The time period for which to retrieve stats. Options: 60-days, 6-months, all"
msgstr "الفترة الزمنية التي تستقبل خلالها الإحصاءات: 60 يوماً ، 6 أشهر ، الكل"

#: class.akismet-rest-api.php:65
msgid "If true, show the number of approved comments beside each comment author in the comments list page."
msgstr "إذا كان ذلك صحيحاً، اعرض عدد التعليقات الموافق عليها بجانب كل كاتب أو ناشر لأي تعليق في صفحة قائمة التعليقات."

#: class.akismet-rest-api.php:60
msgid "If true, Akismet will automatically discard the worst spam automatically rather than putting it in the spam folder."
msgstr "إذا كان ذلك صحيحا، إضافة Akismet سوف تتجاهل تلقائيا أسوأ التعليقات ذات المحتوى المزعج تلقائياً بدلاً من وضعها في مجلد التعليقات المزعجة."

#: class.akismet-rest-api.php:31 class.akismet-rest-api.php:122
#: class.akismet-rest-api.php:135 class.akismet-rest-api.php:148
msgid "A 12-character Akismet API key. Available at akismet.com/get/"
msgstr "مفتاح Akismet API ذو الـ 12 رمز. متوفر على akismet.com/get/"

#: views/notice.php:109
msgid "Your site can&#8217;t connect to the Akismet servers."
msgstr "لم يتمكن موقعك من الاتصال بخوادم Akismet."

#. translators: %s is the wp-config.php file
#: views/predefined.php:7
msgid "An Akismet API key has been defined in the %s file for this site."
msgstr "تم تعريف مفتاح واجهة برمجة تطبيق Akismet في ملف %s لهذا الموقع."

#: views/predefined.php:2
msgid "Manual Configuration"
msgstr "الضبط اليدوي"

#: class.akismet-admin.php:275
msgid "On this page, you are able to update your Akismet settings and view spam stats."
msgstr "يمكنك من خلال هذه الصفحة تحديث إعدادات Akismet وعرض إحصاءات المحتوى المزعج."

#: class.akismet-admin.php:135 class.akismet-admin.php:137
msgid "Akismet Anti-spam"
msgstr "مكافح البريد العشوائي Akismet"

#: views/enter.php:10
msgid "Connect with API key"
msgstr "الاتصال باستخدام مفتاح API"

#. translators: %s is the WordPress.com username
#: views/connect-jp.php:25 views/connect-jp.php:79
msgid "You are connected as %s."
msgstr "تم اتصالك بالمعرّف %s."

#: views/connect-jp.php:10 views/connect-jp.php:18 views/connect-jp.php:38
#: views/connect-jp.php:72 views/connect-jp.php:91
msgid "Connect with Jetpack"
msgstr "الاتصال بـ Jetpack"

#: views/connect-jp.php:12 views/connect-jp.php:32 views/connect-jp.php:67
msgid "Use your Jetpack connection to set up Akismet."
msgstr "استخدم اتصالك بـ Jetpack لإعداد Akismet."

#: views/title.php:2
msgid "Eliminate spam from your site"
msgstr "القضاء على التعليقات والرسائل المزعجة من موقعك."

#. translators: The placeholder is a URL for checking pending comments.
#: views/notice.php:205
msgid "Would you like to <a href=\"%s\">check pending comments</a>?"
msgstr "هل ترغب في <a href=\"%s\">فحص التعليقات التي بانتظار المراجعة</a>؟"

#: views/notice.php:25
msgid "Set up your Akismet account"
msgstr "إعداد حساب Akismet الخاص بك"

#: views/config.php:36
msgid "Detailed stats"
msgstr "إحصاءات تفصيلية"

#: views/config.php:31
msgid "Statistics"
msgstr "إحصائيات"

#: class.akismet-admin.php:1464
msgid "Used by millions, Akismet is quite possibly the best way in the world to <strong>protect your blog from spam</strong>. It keeps your site protected even while you sleep. To get started, just go to <a href=\"admin.php?page=akismet-key-config\">your Akismet Settings page</a> to set up your API key."
msgstr "يستخدمها الملايين، إضافة Akismet هي أفضل طريقة في العالم لـ <strong>حماية موقعك أو مدونتك من التعليقات والرسائل المزعجة والغير مرغوب بها</strong>. فهي تبقي موقعك محميًا حتى عند نومك. وللبدء: توجه فقط إلى <a href=\"admin.php?page=akismet-key-config\">صفحة إعدادات Akismet الخاصة بموقعك</a> لإعداد مفتاح API الخاص بك."

#: class.akismet-admin.php:1462
msgid "Used by millions, Akismet is quite possibly the best way in the world to <strong>protect your blog from spam</strong>. Your site is fully configured and being protected, even while you sleep."
msgstr "يستخدمها الملايين، إضافة Akismet هي أفضل طريقة في العالم لـ <strong>حماية موقعك أو مدونتك من التعليقات والرسائل المزعجة والغير مرغوب بها</strong>. لقد تمت تهيئة موقعك وحمايته بالكامل، حتى أثناء نومك."

#. translators: %s: Number of comments.
#: class.akismet-admin.php:1329
msgid "%s comment was caught as spam."
msgid_plural "%s comments were caught as spam."
msgstr[0] "تمّ اكتشاف تعليق %s كمحتوى مزعج."
msgstr[1] "تمّ اكتشاف تعليق واحد كمحتوى مزعج."
msgstr[2] "تمّ اكتشاف تعليقين كمحتوى مزعج."
msgstr[3] "تمّ اكتشاف %s تعليقات كمحتوى مزعج."
msgstr[4] "تمّ اكتشاف %s تعليق كمحتوى مزعج."
msgstr[5] "تمّ اكتشاف %s تعليق كمحتوى مزعج."

#: class.akismet-admin.php:1326
msgid "No comments were caught as spam."
msgstr "لم يتم إيجاد أي تعليقات مزعجة."

#. translators: %s: Number of comments.
#: class.akismet-admin.php:1322
msgid "Akismet checked %s comment."
msgid_plural "Akismet checked %s comments."
msgstr[0] "قامت إضافة Akismet بفحص %s تعليق."
msgstr[1] "قامت إضافة Akismet بفحص تعليق واحد."
msgstr[2] "قامت إضافة Akismet بفحص تعليقين."
msgstr[3] "قامت إضافة Akismet بفحص %s تعليقات."
msgstr[4] "قامت إضافة Akismet بفحص %s تعليق."
msgstr[5] "قامت إضافة Akismet بفحص %s تعليق."

#: class.akismet-admin.php:1319
msgid "There were no comments to check. Akismet will only check comments awaiting moderation."
msgstr "لم يتم إيجاد تعليقات لفحصها. إضافة Akismet تقوم بفحص التعليقات التي في قائمة الانتظار للمراجعة."

#: class.akismet.php:962
msgid "Comment not found."
msgstr "لم يتم العثور على التعليق."

#. translators: %d: Number of comments.
#: class.akismet-cli.php:89
msgid "%d comment could not be checked."
msgid_plural "%d comments could not be checked."
msgstr[0] "%d تعليق لا يمكن التحقق منه."
msgstr[1] "تعليق واحد لا يمكن التحقق منه."
msgstr[2] "تعليقان لا يمكن التحقق منهما."
msgstr[3] "%d تعليقات لا يمكن التحقق منهم."
msgstr[4] "%d تعليق لا يمكن التحقق منهم."
msgstr[5] "%d تعليق لا يمكن التحقق منهم."

#. translators: %d: Number of comments.
#: class.akismet-cli.php:85
msgid "%d comment moved to Spam."
msgid_plural "%d comments moved to Spam."
msgstr[0] "%d تعليق تمّ نقله إلى قائمة الرسائل المزعجة."
msgstr[1] "تعليق واحد تمّ نقله إلى قائمة الرسائل المزعجة."
msgstr[2] "تعليقان تمّ نقلهما إلى قائمة الرسائل المزعجة."
msgstr[3] "%d تعليقات تمّ نقلها إلى قائمة الرسائل المزعجة."
msgstr[4] "%d تعليق تمّ نقلهم إلى قائمة الرسائل المزعجة."
msgstr[5] "%d تعليق تمّ نقلهم إلى قائمة الرسائل المزعجة."

#. translators: %d: Number of comments.
#: class.akismet-cli.php:82
msgid "Processed %d comment."
msgid_plural "Processed %d comments."
msgstr[0] "لم يتم معالجة أي تعليق."
msgstr[1] "تم معالجة تعليق واحد."
msgstr[2] "تم معالجة تعليقين."
msgstr[3] "تم معالجة %d تعليقات."
msgstr[4] "تم معالجة %d تعليق."
msgstr[5] "تم معالجة %d تعليق."

#. translators: %d: Comment ID.
#: class.akismet-cli.php:45
msgid "Comment #%d could not be checked."
msgstr "لم نتمكن من فحص التعليق #%d."

#. translators: %d: Comment ID.
#: class.akismet-cli.php:42
msgid "Failed to connect to Akismet."
msgstr "فشل الاتصال بـ Akismet."

#. translators: %d: Comment ID.
#: class.akismet-cli.php:39
msgid "Comment #%d is not spam."
msgstr "التعليق #%d ليس مزعجاً."

#. translators: %d: Comment ID.
#: class.akismet-cli.php:36
msgid "Comment #%d is spam."
msgstr "التعليق #%d مزعج."

#. translators: %s: number of false positive spam flagged by Akismet
#: views/config.php:66
msgid "%s false positive"
msgid_plural "%s false positives"
msgstr[0] "%s خطأ حقيقي"
msgstr[1] "%s خطأ حقيقي"
msgstr[2] "%s أخطاء حقيقية"
msgstr[3] "%s أخطاء حقيقية"
msgstr[4] "%s خطأ حقيقي"
msgstr[5] "%s خطأ حقيقي"

#. translators: %s: number of spam missed by Akismet
#: views/config.php:64
msgid "%s missed spam"
msgid_plural "%s missed spam"
msgstr[0] "لا توجد محتويات مزعجة مفقودة"
msgstr[1] "%s محتوى مزعج مفقود"
msgstr[2] "%s محتوى مزعج مفقود"
msgstr[3] "%s محتويات مزعجة مفقودة"
msgstr[4] "%s محتوى مزعج مفقود"
msgstr[5] "%s محتوى مزعج مفقود"

#: views/notice.php:175
msgid "You don&#8217;t have an Akismet plan."
msgstr "أنت لا تملك خطة Akismet."

#: views/notice.php:142
msgid "Your Akismet subscription is suspended."
msgstr "تم إيقاف اشتراكك في Akismet."

#: views/notice.php:131
msgid "Your Akismet plan has been cancelled."
msgstr "لقد تم إلغاء خطة Akismet الخاصة بك."

#. translators: The placeholder is a URL.
#: views/notice.php:124
msgid "We cannot process your payment. Please <a href=\"%s\" target=\"_blank\">update your payment details</a>."
msgstr "لم نتمكن من إتمام عملية الدفع. من فضلك <a href=\"%s\" target=\"_blank\">حدّث تفاصيل الدفع الخاصة بك</a>."

#: views/notice.php:120
msgid "Please update your payment information."
msgstr "يرجى تحديث معلومات الدفع الخاصة بك."

#. translators: %s: Number of minutes.
#: class.akismet-admin.php:1229
msgid "Akismet has saved you %d minute!"
msgid_plural "Akismet has saved you %d minutes!"
msgstr[0] "لقد حفظت Akismet من وقتك!"
msgstr[1] "لقد حفظت Akismet من وقتك دقيقة واحدة!"
msgstr[2] "لقد حفظت Akismet من وقتك دقيقتين!"
msgstr[3] "لقد حفظت Akismet من وقتك %d دقائق!"
msgstr[4] "لقد حفظت Akismet من وقتك %d دقيقة!"
msgstr[5] "لقد حفظت Akismet من وقتك %d دقيقة!"

#. translators: %s: Number of hours.
#: class.akismet-admin.php:1226
msgid "Akismet has saved you %d hour!"
msgid_plural "Akismet has saved you %d hours!"
msgstr[0] "لقد حفظت Akismet من وقتك!"
msgstr[1] "لقد حفظت Akismet من وقتك ساعة واحدة!"
msgstr[2] "لقد حفظت Akismet من وقتك ساعتين!"
msgstr[3] "لقد حفظت Akismet من وقتك %d ساعات!"
msgstr[4] "لقد حفظت Akismet من وقتك %d ساعة!"
msgstr[5] "لقد حفظت Akismet من وقتك %d ساعة!"

#. translators: %s: Number of days.
#: class.akismet-admin.php:1223
msgid "Akismet has saved you %s day!"
msgid_plural "Akismet has saved you %s days!"
msgstr[0] "لقد حفظت Akismet من وقتك!"
msgstr[1] "لقد حفظت Akismet من وقتك يوم واحد!"
msgstr[2] "لقد حفظت Akismet من وقتك يومين!"
msgstr[3] "لقد حفظت Akismet من وقتك %s أيام!"
msgstr[4] "لقد حفظت Akismet من وقتك %s يوم!"
msgstr[5] "لقد حفظت Akismet من وقتك %s يوم!"

#: class.akismet-admin.php:224 class.akismet-admin.php:262
#: class.akismet-admin.php:274
msgid "Akismet filters out spam, so you can focus on more important things."
msgstr "تقوم إضافة Akismet بتنقيح المحتويات المزعجة، لتتمكن من التركيز على ما هو أهم."

#. translators: The placeholder is a URL.
#: views/notice.php:245
msgid "The connection to akismet.com could not be established. Please refer to <a href=\"%s\" target=\"_blank\">our guide about firewalls</a> and check your server configuration."
msgstr "لم نتمكن من تأسيس الاتصال بـ akismet.com. فضلاً راجع <a href=\"%s\" target=\"_blank\">دليلنا عن الجدران الناريّة</a> وافحص إعدادات وضبط الخادم لديك."

#: views/notice.php:239
msgid "The API key you entered could not be verified."
msgstr "لم نتمكن من التحقق من مفتاح API الذي أدخلته."

#: views/config.php:121
msgid "All systems functional."
msgstr "جميع وظائف الأنظمة تعمل."

#: views/config.php:120
msgid "Enabled."
msgstr "مفعّل."

#: views/config.php:118
msgid "Akismet encountered a problem with a previous SSL request and disabled it temporarily. It will begin using SSL for requests again shortly."
msgstr "واجهت إضافة Akismet مشكلة مع طلب SSL سابق وتم التعطيل مؤقتاً. وسيتم البدء باستخدام SSL للطلبات مرة أخرى في أقرب فرصة."

#: views/config.php:117
msgid "Temporarily disabled."
msgstr "تم التعطيل مؤقتاً."

#: views/config.php:112
msgid "Your Web server cannot make SSL requests; contact your Web host and ask them to add support for SSL requests."
msgstr "لايمكن لخادم الويب لديك إجراء طلبات SSL؛ اتصل بمزوّد خدمة الاستضافة واطلب منه إضافة دعم الطلبات التي تستخدم SSL."

#: views/config.php:111
msgid "Disabled."
msgstr "معطّل."

#: views/config.php:108
msgid "SSL status"
msgstr "حالة SSL"

#: class.akismet-admin.php:723
msgid "This comment was reported as not spam."
msgstr "تمت الإشارة إلى أن هذا التعليق ليس مزعجاً."

#: class.akismet-admin.php:715
msgid "This comment was reported as spam."
msgstr "تمت الإشارة إلى أن هذا التعليق مزعج."

#. Author URI of the plugin
#: akismet.php
msgid "https://automattic.com/wordpress-plugins/"
msgstr "https://automattic.com/wordpress-plugins/"

#. Plugin URI of the plugin
#: akismet.php
msgid "https://akismet.com/"
msgstr "https://akismet.com/"

#: views/enter.php:2
msgid "Manually enter an API key"
msgstr "إدخال مفتاح API يدويًا"

#: views/connect-jp.php:53 views/notice.php:333
msgid "Contact Akismet support"
msgstr "اتصّل بفريق الدعم في أكيسميت"

#: views/connect-jp.php:64
msgid "No worries! Get in touch and we&#8217;ll sort this out."
msgstr "لا تقلق! تواصل معنا وسنساعدك في حل المشكلة."

#. translators: %s is the WordPress.com email address
#: views/connect-jp.php:60
msgid "Your subscription for %s is suspended."
msgstr "اشتراكك في %s موقوف."

#. translators: %s is the WordPress.com email address
#: views/connect-jp.php:45
msgid "Your subscription for %s is cancelled."
msgstr "تم إلغاء اشتراكك في %s."

#: views/notice.php:217
msgid "The key you entered is invalid. Please double-check it."
msgstr "يبدو أن المفتاح الذي أدخلته خاطئ. تأكد منه جيداً."

#: views/notice.php:164
msgid "There is a problem with your API key."
msgstr "هناك مشكلة مع مفتاح API الخاص بك."

#. translators: the placeholder is a clickable URL to the Akismet account
#. upgrade page.
#: views/notice.php:157
msgid "You can help us fight spam and upgrade your account by <a href=\"%s\" target=\"_blank\">contributing a token amount</a>."
msgstr "يمكنك مساعدتنا في القضاء على المحتوى المزعج وترقية حسابك <a href=\"%s\" target=\"_blank\">بالمساهمة بمبلغ رمزي</a>."

#. translators: The placeholder is a URL.
#. translators: The placeholder is a URL to the Akismet contact form.
#: views/notice.php:146 views/notice.php:168
msgid "Please contact <a href=\"%s\" target=\"_blank\">Akismet support</a> for assistance."
msgstr "يرجى الاتصال <a href=\"%s\" target=\"_blank\">بفريق دعم Akismet</a> للحصول على المساعدة."

#. translators: The placeholder is a URL.
#: views/notice.php:135
msgid "Please visit your <a href=\"%s\" target=\"_blank\">Akismet account page</a> to reactivate your subscription."
msgstr "يُرجى الاطلاع على <a href=\"%s\" target=\"_blank\">صفحة حساب Akismet</a> لإعادة تفعيل اشتراكك."

#. translators: The placeholder is a URL.
#: views/notice.php:113
msgid "Your firewall may be blocking Akismet from connecting to its API. Please contact your host and refer to <a href=\"%s\" target=\"_blank\">our guide about firewalls</a>."
msgstr "قد يتسبَّب جدار الحماية الخاص بك في حظر إضافة Akismet من الاتصال بـ واجهة API الخاصة بها. يُرجى الاتصال بالمضيف الخاص بك والرجوع إلى <a href=\"%s\" target=\"_blank\">دليلنا عن جدار الحماية</a>."

#. translators: The placeholder is a URL.
#: views/notice.php:102
msgid "Your web host or server administrator has disabled PHP&#8217;s <code>gethostbynamel</code> function.  <strong>Akismet cannot work correctly until this is fixed.</strong>  Please contact your web host or firewall administrator and give them <a href=\"%s\" target=\"_blank\">this information about Akismet&#8217;s system requirements</a>."
msgstr "عطَّل مضيف الويب أو مسؤول الخادم لديك وظائف <code>gethostbynamel</code> لـ PHP. <strong>يتعذر على Akismet العمل بصورة صحيحة حتى يتم حل هذه المشكلة.</strong> يُرجى الاتصال بمضيف الويب أو مسؤول جدار الحماية لديك وتقديم هذه المعلومات له  <a href=\"%s\" target=\"_blank\">حول متطلبات النظام لإضافة Akismet</a>."

#: views/notice.php:98
msgid "Network functions are disabled."
msgstr "خيارات الشبكة معطّلة."

#. translators: the placeholder is a clickable URL that leads to more
#. information regarding an error code.
#: views/notice.php:83
msgid "For more information: %s"
msgstr "لمزيد من المعلومات: %s"

#: views/notice.php:37
msgid "Some comments have not yet been checked for spam by Akismet. They have been temporarily held for moderation and will automatically be rechecked later."
msgstr "لم يتم التحقق بعد من بعض التعليقات المزعجة باستخدام أكيسميت. تم احتجاز هذه التعليقات مؤقتًا لمراجعتها وستتم إعادة التحقق منها لاحقًا."

#: views/notice.php:36 views/notice.php:46
msgid "Akismet has detected a problem."
msgstr "أكيسميت واجهت مشكلة."

#: views/config.php:312
msgid "Change"
msgstr "تغيير"

#: views/config.php:312
msgid "Upgrade"
msgstr "ترقية"

#: views/config.php:286
msgid "Active"
msgstr "فعّال"

#: views/config.php:282
msgid "Missing"
msgstr "مفقود"

#: views/config.php:280
msgid "Suspended"
msgstr "موقوف"

#: views/config.php:278
msgid "Cancelled"
msgstr "ملغي"

#: views/config.php:241
msgid "Disconnect this account"
msgstr "قطع الاتصال بهذا الحساب"

#: views/config.php:180
msgid "Note:"
msgstr "ملاحظة:"

#: views/config.php:173
msgid "Always put spam in the Spam folder for review."
msgstr "وضع الرسائل المزعجة دائمًا في مجلد المحتوى المزعج لمراجعتها."

#: views/config.php:165
msgid "Silently discard the worst and most pervasive spam so I never see it."
msgstr "تجاهل أسوأ الرسائل المزعجة وأكثرها انتشارًا في صمت حتى لا أراها بعد ذلك أبدًا."

#: views/config.php:159
msgid "Akismet Anti-spam strictness"
msgstr "صرامة أكيسميت في مكافحة البريد المزعج"

#: views/config.php:59
msgid "Accuracy"
msgstr "الدقّة"

#: views/config.php:54
msgid "All time"
msgstr "كل الأوقات"

#: views/config.php:51 views/config.php:56
msgid "Spam blocked"
msgid_plural "Spam blocked"
msgstr[0] "لم يتمّ حظر أي محتوى مزعج"
msgstr[1] "محتوى مزعج تمّ حظره"
msgstr[2] "محتوى مزعج تمّ حظره"
msgstr[3] "محتوى مزعج تمّ حظره"
msgstr[4] "محتوى مزعج تمّ حظره"
msgstr[5] "محتوى مزعج تمّ حظره"

#: views/config.php:49
msgid "Past six months"
msgstr "آخر 6 أشهر ماضية"

#. translators: 1: WordPress documentation URL, 2: Akismet download URL.
#: class.akismet.php:1900
msgid "Please <a href=\"%1$s\">upgrade WordPress</a> to a current version, or <a href=\"%2$s\">downgrade to version 2.4 of the Akismet plugin</a>."
msgstr "يُرجى <a href=\"%1$s\">ترقية ووردبريس</a> للإصدار الحالي أو <a href=\"%2$s\">الرجوع إلى الإصدار السابق 2.4 من إضافة أكيسميت</a>."

#. translators: 1: Current Akismet version number, 2: Minimum WordPress version
#. number required.
#: class.akismet.php:1898
msgid "Akismet %1$s requires WordPress %2$s or higher."
msgstr "يتطلب أكيسميت ⁦%1$s⁩ ووردبريس ⁦%2$s⁩ أو إصدارًا أعلى."

#: class.akismet-admin.php:730
msgid "Akismet cleared this comment during an automatic retry."
msgstr "أكيسميت أجازت هذا التعليق أثناء إعادة المحاولة تلقائياً."

#: class.akismet-admin.php:727
msgid "Akismet caught this comment as spam during an automatic retry."
msgstr "أشارت أكيسميت أثناء محاولة تلقائية إلى أن هذا التعليق مزعج."

#. translators: The placeholder is a username.
#: class.akismet-admin.php:721
msgid "%s reported this comment as not spam."
msgstr "%s  أشار إلى هذا التعليق بأنه غير مزعج. "

#. translators: The placeholder is a username.
#: class.akismet-admin.php:713
msgid "%s reported this comment as spam."
msgstr "%s أشار إلى هذا التعليق بأنه مزعج."

#. translators: %1$s is a username; %2$s is a short string (like 'spam' or
#. 'approved') denoting the new comment status.
#: class.akismet-admin.php:778
msgid "%1$s changed the comment status to %2$s."
msgstr "%1$s غيّر حالة التعليق إلى %2$s."

#. translators: The placeholder is an error response returned by the API
#. server.
#: class.akismet-admin.php:735
msgid "Akismet was unable to check this comment (response: %s) but will automatically retry later."
msgstr "لم يتمكن Akismet من فحص هذا التعليق (الاستجابة: %s) لكنه سيعاود الفحص التلقائي لاحقاً."

#: class.akismet-admin.php:697
msgid "Akismet cleared this comment."
msgstr "أعاد Akismet فحص هذا التعليق."

#. translators: The placeholder is a short string (like 'spam' or 'approved')
#. denoting the new comment status.
#: class.akismet-admin.php:772
msgid "Comment status was changed to %s"
msgstr "تمّ تغيير حالة التعليق إلى %s"

#: class.akismet-admin.php:691
msgid "Akismet caught this comment as spam."
msgstr "وجَد Akismet هذا التعليق وعدّهُ مزعجاً."

#. translators: The placeholder is the number of pieces of spam blocked by
#. Akismet.
#: class.akismet-widget.php:146
msgid "<strong class=\"count\">%1$s spam</strong> blocked by <strong>Akismet</strong>"
msgid_plural "<strong class=\"count\">%1$s spam</strong> blocked by <strong>Akismet</strong>"
msgstr[0] "<strong class=\"count\">%1$s تعليقات مزعجة</strong> حُجبت بواسطة <strong>أكيسميت</strong>"
msgstr[1] "<strong class=\"count\">تعليق مزعج واحد</strong> حُجب بواسطة <strong>أكيسميت</strong>"
msgstr[2] "<strong class=\"count\">تعليقين مزعجين</strong> حُجبا بواسطة <strong>أكيسميت</strong>"
msgstr[3] "<strong class=\"count\">%1$s تعليقات مزعجة</strong> حُجبت بواسطة <strong>أكيسميت</strong>"
msgstr[4] "<strong class=\"count\">%1$s تعليق</strong> حُجب بواسطة <strong>أكيسميت</strong>"
msgstr[5] "<strong class=\"count\">%1$s تعليق</strong> حُجب بواسطة <strong>أكيسميت</strong>"

#: class.akismet-widget.php:39
msgid "Title:"
msgstr "العنوان:"

#: class.akismet-widget.php:34 class.akismet-widget.php:69
msgid "Spam Blocked"
msgstr "الرسائل المزعجة المحظورة"

#: class.akismet-widget.php:21
msgid "Display the number of spam comments Akismet has caught"
msgstr "عرض عدد التعليقات المزعجة التي أمسكتها أكيسميت"

#: class.akismet-widget.php:20
msgid "Akismet Widget"
msgstr "مربع جانبي أكيسميت"

#: class.akismet-admin.php:1219
msgid "Cleaning up spam takes time."
msgstr "عملية تنظيف التعليقات المزعجة قد تتطلّب بعضًا من الوقت."

#. translators: The Akismet configuration page URL.
#: class.akismet-admin.php:1091
msgid "Please check your <a href=\"%s\">Akismet configuration</a> and contact your web host if problems persist."
msgstr "يُرجى التحقق من <a href=\"%s\">تكوين أكيسميت</a> والاتصال بمضيف الويب الخاص بك إذا استمرت المشاكل."

#. translators: The placeholder is an amount of time, like "7 seconds" or "3
#. days" returned by the function human_time_diff().
#: class.akismet-admin.php:792
msgid "%s ago"
msgstr "منذ %s"

#. translators: %s: Number of comments.
#: class.akismet-admin.php:664
msgid "%s approved"
msgid_plural "%s approved"
msgstr[0] "%s تمّ الموافقة عليها"
msgstr[1] "%s تمّ الموافقة عليها"
msgstr[2] "%s تمّ الموافقة عليها"
msgstr[3] "%s تمّ الموافقة عليها"
msgstr[4] "%s تمّ الموافقة عليها"
msgstr[5] "%s تمّ الموافقة عليها"

#: class.akismet-admin.php:638
msgid "History"
msgstr "السّجل"

#: class.akismet-admin.php:638 class.akismet-admin.php:646
msgid "View comment history"
msgstr "مشاهدة سجّل التعليق"

#. translators: %s: Username.
#: class.akismet-admin.php:625
msgid "Un-spammed by %s"
msgstr "صُنّف بأنه غير مزعج بواسطة %s"

#. translators: %s: Username.
#: class.akismet-admin.php:622
msgid "Flagged as spam by %s"
msgstr "تمّت الإشارة إليه كمحتوى مزعج (سبام) بواسطة %s "

#: class.akismet-admin.php:616
msgid "Cleared by Akismet"
msgstr "حُذفت بواسطة Akismet"

#: class.akismet-admin.php:614
msgid "Flagged as spam by Akismet"
msgstr "تمّت الإشارة إليه كتعليق مزعج (سبام) بواسطة أكيسميت"

#: class.akismet-admin.php:610
msgid "Awaiting spam check"
msgstr "في انتظار التحقّق من المحتوى المزعج"

#. translators: The placeholder is an error response returned by the API
#. server.
#: class.akismet-admin.php:743
msgid "Akismet was unable to recheck this comment (response: %s)."
msgstr "لم يتمكن Akismet من إعادة فحص هذا التعليق (الاستجابة: %s)."

#: class.akismet-admin.php:694
msgid "Akismet re-checked and cleared this comment."
msgstr "أعاد Akismet فحص هذا التعليق وعدّه غير مزعج."

#: class.akismet-admin.php:688
msgid "Akismet re-checked and caught this comment as spam."
msgstr "أعاد Akismet فحص هذا التعليق وعدّه مزعجاً."

#: class.akismet-admin.php:498
msgid "Check for Spam"
msgstr "تحقق من الإعلانات المزعجة"

#. translators: %s: Comments page URL.
#: class.akismet-admin.php:440
msgid "There&#8217;s nothing in your <a href='%s'>spam queue</a> at the moment."
msgstr "لا يوجد شيء في <a href='%s'>قائمة انتظار التعليقات المزعجة</a> الخاصة بك حالياً."

#. translators: 1: Number of comments, 2: Comments page URL.
#: class.akismet-admin.php:429
msgid "There&#8217;s <a href=\"%2$s\">%1$s comment</a> in your spam queue right now."
msgid_plural "There are <a href=\"%2$s\">%1$s comments</a> in your spam queue right now."
msgstr[0] "لا يوجد لديك <a href=\"%2$s\">أي تعليق</a> في قائمة انتظار التعليقات المزعجة في الوقت الحالي."
msgstr[1] "هناك <a href=\"%2$s\">تعليق واحد (1)</a> في قائمة انتظار التعليقات المزعجة في الوقت الحالي."
msgstr[2] "هناك <a href=\"%2$s\">تعليقان (2)</a> في قائمة انتظار التعليقات المزعجة في الوقت الحالي."
msgstr[3] "توجد <a href=\"%2$s\">%1$s تعليقات</a> في قائمة انتظار التعليقات المزعجة في الوقت الحالي."
msgstr[4] "توجد <a href=\"%2$s\">%1$s تعليق</a> في قائمة انتظار التعليقات المزعجة في الوقت الحالي."
msgstr[5] "توجد <a href=\"%2$s\">%1$s تعليق</a> في قائمة انتظار التعليقات المزعجة في الوقت الحالي."

#. translators: %s: Akismet website URL.
#: class.akismet-admin.php:421
msgid "<a href=\"%s\">Akismet</a> blocks spam from getting to your blog. "
msgstr "تقوم إضافة <a href=\"%s\">Akismet</a> بحجب التعليقات ذات المحتوى المزعج من الوصول إلى موقعك. "

#. translators: 1: Akismet website URL, 2: Number of spam comments.
#: class.akismet-admin.php:410
msgid "<a href=\"%1$s\">Akismet</a> has protected your site from %2$s spam comment already. "
msgid_plural "<a href=\"%1$s\">Akismet</a> has protected your site from %2$s spam comments already. "
msgstr[0] "تقوم إضافة <a href=\"%1$s\">Akismet</a> بحماية موقعك من التعليقات المزعجة، لا توجد تعليقات مزعجة حتى الآن."
msgstr[1] "قامت إضافة <a href=\"%1$s\">Akismet</a> بحماية موقعك من تعليق واحد مزعج."
msgstr[2] "قامت إضافة <a href=\"%1$s\">Akismet</a> بحماية موقعك من تعليقين مزعجين."
msgstr[3] "قامت إضافة <a href=\"%1$s\">Akismet</a> بحماية موقعك من %2$s تعليقات مزعجة."
msgstr[4] "قامت إضافة <a href=\"%1$s\">Akismet</a> بحماية موقعك من %2$s تعليق مزعج."
msgstr[5] "قامت إضافة <a href=\"%1$s\">Akismet</a> بحماية موقعك من %2$s تعليق مزعج."

#. translators: 1: Akismet website URL, 2: Comments page URL, 3: Number of spam
#. comments.
#: class.akismet-admin.php:393
msgid "<a href=\"%1$s\">Akismet</a> has protected your site from <a href=\"%2$s\">%3$s spam comment</a>."
msgid_plural "<a href=\"%1$s\">Akismet</a> has protected your site from <a href=\"%2$s\">%3$s spam comments</a>."
msgstr[0] "<a href=\"%1$s\">لقد قام أكيسميت</a> بحماية موقعك من <a href=\"%2$s\">%3$s تعليق مزعج</a>."
msgstr[1] "<a href=\"%1$s\">لقد قام أكيسميت</a> بحماية موقعك من <a href=\"%2$s\">%3$s تعليق مزعج</a>."
msgstr[2] "<a href=\"%1$s\">لقد قام أكيسميت</a> بحماية موقعك من <a href=\"%2$s\">%3$s تعليق مزعج</a>."
msgstr[3] "<a href=\"%1$s\">لقد قام أكيسميت</a> بحماية موقعك من <a href=\"%2$s\">%3$s تعليقات مزعجة</a>."
msgstr[4] "<a href=\"%1$s\">لقد قام أكيسميت</a> بحماية موقعك من <a href=\"%2$s\">%3$s تعليقات مزعجة</a>."
msgstr[5] "<a href=\"%1$s\">لقد قام أكيسميت</a> بحماية موقعك من <a href=\"%2$s\">%3$s تعليقات مزعجة</a>."

#: class.akismet-admin.php:389
msgctxt "comments"
msgid "Spam"
msgstr "مزعج"

#: class.akismet-admin.php:316
msgid "Cheatin&#8217; uh?"
msgstr "تتحايل، ها؟"

#: class.akismet-admin.php:310
msgid "Akismet Support"
msgstr "دعم Akismet"

#: class.akismet-admin.php:309
msgid "Akismet FAQ"
msgstr "الأسئلة المتداولة حول Akismet"

#: class.akismet-admin.php:308
msgid "For more information:"
msgstr "للمزيد من المعلومات:"

#: class.akismet-admin.php:299
msgid "The subscription status - active, cancelled or suspended"
msgstr "حالة الاشتراك - مفعّل أو ملغى أو معلق"

#: class.akismet-admin.php:299 views/config.php:274
msgid "Status"
msgstr "الحالة"

#: class.akismet-admin.php:298
msgid "The Akismet subscription plan"
msgstr "خطة الاشتراك في Akismet"

#: class.akismet-admin.php:298
msgid "Subscription Type"
msgstr "نوع الاشتراك"

#: class.akismet-admin.php:295 views/config.php:260
msgid "Account"
msgstr "الحساب"

#: class.akismet-admin.php:287
msgid "Choose to either discard the worst spam automatically or to always put all spam in spam folder."
msgstr "اختر إما تجاهل أسوأ محتوى مزعج تلقائيًا أو وضع كل الرسائل المزعجة في مجلد المحتوى المزعج دومًا."

#: class.akismet-admin.php:287
msgid "Strictness"
msgstr "الدقة"

#: class.akismet-admin.php:286
msgid "Show the number of approved comments beside each comment author in the comments list page."
msgstr "إظهار عدد التعليقات التي قمت بالموافقة عليها بجانب اسم كل كاتب تعليق بصفحة التعليقات."

#: class.akismet-admin.php:286 views/config.php:131
msgid "Comments"
msgstr "تعليقات"

#: class.akismet-admin.php:285
msgid "Enter/remove an API key."
msgstr "إدخال/إزالة مفتاح API."

#: class.akismet-admin.php:285
msgid "API Key"
msgstr "مفتاح (API)"

#: class.akismet-admin.php:273 class.akismet-admin.php:284
#: class.akismet-admin.php:297
msgid "Akismet Configuration"
msgstr "إعدادات Akismet"

#: class.akismet-admin.php:263
msgid "On this page, you are able to view stats on spam filtered on your site."
msgstr "من خلال هذه الصفحة، ستكون قادرًا على عرض الإحصاءات بشأن التعليقات المزعجة التي تمت تصفيتها على موقعك."

#: class.akismet-admin.php:261
msgid "Akismet Stats"
msgstr "إحصائيات Akismet"

#: class.akismet-admin.php:250
msgid "Click the Use this Key button."
msgstr "انقر على زر \"استخدام هذا المفتاح\"."

#: class.akismet-admin.php:249
msgid "Copy and paste the API key into the text field."
msgstr "انسخ مفتاح API والصقه في الحقل النصي."

#: class.akismet-admin.php:247
msgid "If you already have an API key"
msgstr "إذا كان لديك مفتاح API بالفعل"

#: class.akismet-admin.php:244
msgid "Enter an API Key"
msgstr "أدخل مفتاح (API)"

#. translators: %s: a link to the signup page with the text 'Akismet.com'.
#: class.akismet-admin.php:237
msgid "Sign up for an account on %s to get an API Key."
msgstr "قم بتسجيل حساب على %s للحصول على مفتاح API."

#: class.akismet-admin.php:235
msgid "You need to enter an API key to activate the Akismet service on your site."
msgstr "تحتاج إلى إدخال مفتاح (API) لتفعيل خدمة أكيسميت في موقعك."

#: class.akismet-admin.php:232
msgid "New to Akismet"
msgstr "هل أنت جديد على Akismet"

#: class.akismet-admin.php:225
msgid "On this page, you are able to set up the Akismet plugin."
msgstr "من خلال هذه الصفحة، ستكون قادرًا على إعداد إضافة Akismet."

#: class.akismet-admin.php:223 class.akismet-admin.php:234
#: class.akismet-admin.php:246
msgid "Akismet Setup"
msgstr "إعداد Akismet"

#: class.akismet-admin.php:221 class.akismet-admin.php:259
#: class.akismet-admin.php:271
msgid "Overview"
msgstr "نظرة عامة"

#: class.akismet-admin.php:190
msgid "Re-adding..."
msgstr "جارِ إعادة الإضافة..."

#: class.akismet-admin.php:189
msgid "(undo)"
msgstr "(تراجع)"

#: class.akismet-admin.php:188
msgid "URL removed"
msgstr "تم حذف الرابط"

#: class.akismet-admin.php:187
msgid "Removing..."
msgstr "جارِ الإزالة..."

#: class.akismet-admin.php:186
msgid "Remove this URL"
msgstr "إزالة عنوان URL هذا"

#: class.akismet-admin.php:107 class.akismet-admin.php:1479
msgid "Akismet"
msgstr "أكيسميت"

#: class.akismet-admin.php:128 class.akismet-admin.php:282
#: class.akismet-admin.php:819 views/config.php:83
msgid "Settings"
msgstr "إعدادات"

#: class.akismet-admin.php:103
msgid "Comment History"
msgstr "سِجّل التعليق"