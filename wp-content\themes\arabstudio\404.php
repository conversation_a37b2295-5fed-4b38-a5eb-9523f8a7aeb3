<?php
/**
 * صفحة الخطأ 404 - Arab Studio Theme
 * 404 Error Page Template
 */

get_header(); ?>

<div class="animated-bg"></div>

<main class="main-content error-404-page">
    <div class="container">
        <div class="error-404-content">
            
            <!-- رسم ثلاثي الأبعاد للخطأ 404 -->
            <div class="error-visual">
                <canvas id="error-canvas" class="error-canvas"></canvas>
                <div class="error-number">404</div>
            </div>
            
            <!-- محتوى الخطأ -->
            <div class="error-content">
                <h1 class="error-title">عذراً، الصفحة غير موجودة!</h1>
                <p class="error-description">
                    يبدو أن الصفحة التي تبحث عنها قد تم نقلها أو حذفها أو أن الرابط غير صحيح.
                    لا تقلق، يمكنك العثور على ما تبحث عنه من خلال الروابط أدناه.
                </p>
                
                <!-- أزرار التنقل -->
                <div class="error-actions">
                    <a href="<?php echo home_url('/'); ?>" class="btn btn-primary">
                        <i class="fas fa-home"></i>
                        العودة للرئيسية
                    </a>
                    <a href="javascript:history.back()" class="btn btn-secondary">
                        <i class="fas fa-arrow-right"></i>
                        الصفحة السابقة
                    </a>
                </div>
                
                <!-- مربع البحث -->
                <div class="error-search">
                    <h3>أو ابحث عما تريد:</h3>
                    <form role="search" method="get" class="search-form" action="<?php echo home_url('/'); ?>">
                        <div class="search-input-group">
                            <input type="search" 
                                   class="search-field" 
                                   placeholder="ابحث في الموقع..." 
                                   value="<?php echo get_search_query(); ?>" 
                                   name="s" 
                                   required>
                            <button type="submit" class="search-submit">
                                <i class="fas fa-search"></i>
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
        
        <!-- روابط مفيدة -->
        <div class="helpful-links">
            <h3>روابط مفيدة:</h3>
            <div class="links-grid">
                <div class="link-item">
                    <a href="<?php echo home_url('/'); ?>">
                        <i class="fas fa-home"></i>
                        <span>الرئيسية</span>
                    </a>
                </div>
                
                <div class="link-item">
                    <a href="<?php echo home_url('/services/'); ?>">
                        <i class="fas fa-cogs"></i>
                        <span>خدماتنا</span>
                    </a>
                </div>
                
                <div class="link-item">
                    <a href="<?php echo home_url('/about/'); ?>">
                        <i class="fas fa-info-circle"></i>
                        <span>عن الشركة</span>
                    </a>
                </div>
                
                <div class="link-item">
                    <a href="<?php echo home_url('/portfolio/'); ?>">
                        <i class="fas fa-briefcase"></i>
                        <span>أعمالنا</span>
                    </a>
                </div>
                
                <div class="link-item">
                    <a href="<?php echo home_url('/blog/'); ?>">
                        <i class="fas fa-blog"></i>
                        <span>المدونة</span>
                    </a>
                </div>
                
                <div class="link-item">
                    <a href="<?php echo home_url('/contact/'); ?>">
                        <i class="fas fa-envelope"></i>
                        <span>تواصل معنا</span>
                    </a>
                </div>
            </div>
        </div>
        
        <!-- معلومات التواصل السريع -->
        <div class="quick-contact">
            <h3>تحتاج مساعدة؟ تواصل معنا:</h3>
            <div class="contact-options">
                <a href="tel:+964XXXXXXXXX" class="contact-option phone">
                    <i class="fas fa-phone"></i>
                    <span>اتصل بنا</span>
                </a>
                
                <a href="mailto:<EMAIL>" class="contact-option email">
                    <i class="fas fa-envelope"></i>
                    <span>راسلنا</span>
                </a>
                
                <a href="https://wa.me/964XXXXXXXXX" class="contact-option whatsapp" target="_blank">
                    <i class="fab fa-whatsapp"></i>
                    <span>واتساب</span>
                </a>
            </div>
        </div>
    </div>
</main>

<style>
/* أنماط صفحة الخطأ 404 */
.error-404-page {
    padding: 6rem 0 4rem;
    min-height: 100vh;
    display: flex;
    align-items: center;
}

.error-404-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: center;
    margin-bottom: 4rem;
}

.error-visual {
    position: relative;
    height: 400px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.error-canvas {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 1;
}

.error-number {
    font-size: 8rem;
    font-weight: bold;
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    z-index: 2;
    position: relative;
    text-shadow: 0 0 30px rgba(220, 38, 38, 0.5);
    animation: errorGlow 2s ease-in-out infinite alternate;
}

@keyframes errorGlow {
    from { 
        filter: drop-shadow(0 0 20px rgba(220, 38, 38, 0.3));
        transform: scale(1);
    }
    to { 
        filter: drop-shadow(0 0 40px rgba(220, 38, 38, 0.6));
        transform: scale(1.05);
    }
}

.error-content {
    text-align: center;
}

.error-title {
    font-size: 2.5rem;
    font-weight: bold;
    color: var(--white);
    margin-bottom: 1.5rem;
    line-height: 1.2;
}

.error-description {
    font-size: 1.1rem;
    color: var(--light-gray);
    line-height: 1.6;
    margin-bottom: 2rem;
}

.error-actions {
    display: flex;
    gap: 1rem;
    justify-content: center;
    margin-bottom: 3rem;
    flex-wrap: wrap;
}

.error-search {
    background: rgba(31, 41, 55, 0.8);
    padding: 2rem;
    border-radius: 15px;
    border: 1px solid rgba(220, 38, 38, 0.1);
}

.error-search h3 {
    color: var(--white);
    font-size: 1.3rem;
    margin-bottom: 1rem;
    text-align: center;
}

.search-input-group {
    display: flex;
    gap: 0;
    max-width: 400px;
    margin: 0 auto;
}

.search-field {
    flex: 1;
    padding: 1rem;
    background: rgba(17, 24, 39, 0.8);
    border: 1px solid rgba(107, 114, 128, 0.3);
    border-radius: 10px 0 0 10px;
    color: var(--white);
    font-size: 1rem;
}

.search-field:focus {
    outline: none;
    border-color: var(--primary-red);
}

.search-field::placeholder {
    color: var(--light-gray);
}

.search-submit {
    padding: 1rem 1.5rem;
    background: var(--gradient-primary);
    border: none;
    border-radius: 0 10px 10px 0;
    color: var(--white);
    cursor: pointer;
    transition: all 0.3s ease;
}

.search-submit:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-glow);
}

.helpful-links {
    background: rgba(31, 41, 55, 0.8);
    padding: 3rem;
    border-radius: 20px;
    border: 1px solid rgba(220, 38, 38, 0.1);
    margin-bottom: 3rem;
    text-align: center;
}

.helpful-links h3 {
    color: var(--white);
    font-size: 1.5rem;
    margin-bottom: 2rem;
}

.links-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 1.5rem;
}

.link-item a {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.5rem;
    padding: 1.5rem;
    background: rgba(17, 24, 39, 0.8);
    border-radius: 15px;
    text-decoration: none;
    color: var(--white);
    transition: all 0.3s ease;
    border: 1px solid rgba(107, 114, 128, 0.2);
}

.link-item a:hover {
    transform: translateY(-5px);
    background: rgba(220, 38, 38, 0.1);
    border-color: var(--primary-red);
    box-shadow: var(--shadow-glow);
}

.link-item i {
    font-size: 2rem;
    color: var(--primary-red);
}

.link-item span {
    font-weight: 500;
}

.quick-contact {
    background: rgba(31, 41, 55, 0.8);
    padding: 3rem;
    border-radius: 20px;
    border: 1px solid rgba(220, 38, 38, 0.1);
    text-align: center;
}

.quick-contact h3 {
    color: var(--white);
    font-size: 1.5rem;
    margin-bottom: 2rem;
}

.contact-options {
    display: flex;
    justify-content: center;
    gap: 2rem;
    flex-wrap: wrap;
}

.contact-option {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 1rem 2rem;
    border-radius: 50px;
    text-decoration: none;
    color: var(--white);
    transition: all 0.3s ease;
    font-weight: 500;
}

.contact-option.phone {
    background: linear-gradient(135deg, #10b981, #059669);
}

.contact-option.email {
    background: linear-gradient(135deg, #3b82f6, #1d4ed8);
}

.contact-option.whatsapp {
    background: linear-gradient(135deg, #25d366, #128c7e);
}

.contact-option:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
}

/* تجاوب الشاشات */
@media (max-width: 768px) {
    .error-404-content {
        grid-template-columns: 1fr;
        gap: 2rem;
        text-align: center;
    }
    
    .error-number {
        font-size: 6rem;
    }
    
    .error-title {
        font-size: 2rem;
    }
    
    .error-actions {
        flex-direction: column;
        align-items: center;
    }
    
    .btn {
        width: 100%;
        max-width: 300px;
    }
    
    .links-grid {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .contact-options {
        flex-direction: column;
        align-items: center;
    }
    
    .contact-option {
        width: 100%;
        max-width: 250px;
        justify-content: center;
    }
}

@media (max-width: 480px) {
    .error-number {
        font-size: 4rem;
    }
    
    .error-title {
        font-size: 1.8rem;
    }
    
    .helpful-links,
    .quick-contact,
    .error-search {
        padding: 2rem 1rem;
        margin: 0 1rem 2rem;
    }
    
    .links-grid {
        grid-template-columns: 1fr;
    }
}
</style>

<script>
// أنيميشن ثلاثي الأبعاد لصفحة الخطأ 404
document.addEventListener('DOMContentLoaded', function() {
    const canvas = document.getElementById('error-canvas');
    if (canvas && typeof THREE !== 'undefined') {
        const scene = new THREE.Scene();
        const camera = new THREE.PerspectiveCamera(75, canvas.clientWidth / canvas.clientHeight, 0.1, 1000);
        const renderer = new THREE.WebGLRenderer({ canvas: canvas, alpha: true });
        
        renderer.setSize(canvas.clientWidth, canvas.clientHeight);
        renderer.setPixelRatio(Math.min(window.devicePixelRatio, 2));
        
        // إنشاء شكل هندسي متكسر
        const geometry = new THREE.BoxGeometry(1, 1, 1);
        const material = new THREE.MeshBasicMaterial({
            color: 0xdc2626,
            wireframe: true,
            transparent: true,
            opacity: 0.6
        });
        
        const cubes = [];
        for (let i = 0; i < 10; i++) {
            const cube = new THREE.Mesh(geometry, material);
            cube.position.set(
                (Math.random() - 0.5) * 6,
                (Math.random() - 0.5) * 6,
                (Math.random() - 0.5) * 6
            );
            cube.rotation.set(
                Math.random() * Math.PI,
                Math.random() * Math.PI,
                Math.random() * Math.PI
            );
            scene.add(cube);
            cubes.push(cube);
        }
        
        camera.position.z = 8;
        
        function animate() {
            requestAnimationFrame(animate);
            
            cubes.forEach((cube, index) => {
                cube.rotation.x += 0.01 * (index + 1);
                cube.rotation.y += 0.01 * (index + 1);
                cube.position.y += Math.sin(Date.now() * 0.001 + index) * 0.01;
            });
            
            renderer.render(scene, camera);
        }
        
        animate();
        
        // تجاوب الشاشة
        window.addEventListener('resize', () => {
            camera.aspect = canvas.clientWidth / canvas.clientHeight;
            camera.updateProjectionMatrix();
            renderer.setSize(canvas.clientWidth, canvas.clientHeight);
        });
    }
});
</script>

<?php get_footer(); ?>
