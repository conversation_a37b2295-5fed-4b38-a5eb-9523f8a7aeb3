<?php
/**
 * Arab Studio Theme Functions
 * ثيم عرب استوديو - الوظائف الأساسية
 */

// منع الوصول المباشر
if (!defined('ABSPATH')) {
    exit;
}

// إعداد الثيم
function arabstudio_setup() {
    // دعم العنوان التلقائي
    add_theme_support('title-tag');
    
    // دعم الصور المميزة
    add_theme_support('post-thumbnails');
    
    // دعم القوائم المخصصة
    add_theme_support('menus');
    
    // دعم HTML5
    add_theme_support('html5', array(
        'search-form',
        'comment-form',
        'comment-list',
        'gallery',
        'caption',
    ));
    
    // دعم الخلفيات المخصصة
    add_theme_support('custom-background', array(
        'default-color' => '111827',
    ));
    
    // دعم الشعار المخصص
    add_theme_support('custom-logo', array(
        'height'      => 100,
        'width'       => 300,
        'flex-height' => true,
        'flex-width'  => true,
    ));
    
    // تسجيل القوائم
    register_nav_menus(array(
        'primary' => __('القائمة الرئيسية', 'arabstudio'),
        'footer'  => __('قائمة الفوتر', 'arabstudio'),
    ));
}
add_action('after_setup_theme', 'arabstudio_setup');

// تحميل الأنماط والسكريبتات
function arabstudio_scripts() {
    // تحميل خط Cairo من Google Fonts
    wp_enqueue_style('google-fonts', 'https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700;800;900&display=swap', array(), null);
    
    // تحميل Font Awesome
    wp_enqueue_style('font-awesome', 'https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css', array(), '6.4.0');
    
    // تحميل أنماط الثيم
    wp_enqueue_style('arabstudio-style', get_stylesheet_uri(), array(), '1.0.0');

    // تحميل ملف التجاوب المتقدم
    wp_enqueue_style('arabstudio-responsive', get_template_directory_uri() . '/responsive.css', array('arabstudio-style'), '1.0.0');
    
    // تحميل Three.js للأنيميشن ثلاثي الأبعاد
    wp_enqueue_script('threejs', 'https://cdnjs.cloudflare.com/ajax/libs/three.js/r128/three.min.js', array(), 'r128', true);
    
    // تحميل GSAP للأنيميشن المتقدم
    wp_enqueue_script('gsap', 'https://cdnjs.cloudflare.com/ajax/libs/gsap/3.12.2/gsap.min.js', array(), '3.12.2', true);
    
    // تحميل سكريبت الثيم المخصص
    wp_enqueue_script('arabstudio-script', get_template_directory_uri() . '/js/main.js', array('jquery', 'threejs', 'gsap'), '1.0.0', true);
    
    // تمرير البيانات للجافاسكريبت
    wp_localize_script('arabstudio-script', 'arabstudio_ajax', array(
        'ajax_url' => admin_url('admin-ajax.php'),
        'nonce'    => wp_create_nonce('arabstudio_nonce'),
    ));
}
add_action('wp_enqueue_scripts', 'arabstudio_scripts');

// تسجيل مناطق الودجت
function arabstudio_widgets_init() {
    register_sidebar(array(
        'name'          => __('الشريط الجانبي', 'arabstudio'),
        'id'            => 'sidebar-1',
        'description'   => __('إضافة ودجت هنا.', 'arabstudio'),
        'before_widget' => '<section id="%1$s" class="widget %2$s">',
        'after_widget'  => '</section>',
        'before_title'  => '<h2 class="widget-title">',
        'after_title'   => '</h2>',
    ));
    
    register_sidebar(array(
        'name'          => __('فوتر 1', 'arabstudio'),
        'id'            => 'footer-1',
        'description'   => __('منطقة الفوتر الأولى', 'arabstudio'),
        'before_widget' => '<div id="%1$s" class="widget %2$s">',
        'after_widget'  => '</div>',
        'before_title'  => '<h3 class="widget-title">',
        'after_title'   => '</h3>',
    ));
    
    register_sidebar(array(
        'name'          => __('فوتر 2', 'arabstudio'),
        'id'            => 'footer-2',
        'description'   => __('منطقة الفوتر الثانية', 'arabstudio'),
        'before_widget' => '<div id="%1$s" class="widget %2$s">',
        'after_widget'  => '</div>',
        'before_title'  => '<h3 class="widget-title">',
        'after_title'   => '</h3>',
    ));
    
    register_sidebar(array(
        'name'          => __('فوتر 3', 'arabstudio'),
        'id'            => 'footer-3',
        'description'   => __('منطقة الفوتر الثالثة', 'arabstudio'),
        'before_widget' => '<div id="%1$s" class="widget %2$s">',
        'after_widget'  => '</div>',
        'before_title'  => '<h3 class="widget-title">',
        'after_title'   => '</h3>',
    ));
}
add_action('widgets_init', 'arabstudio_widgets_init');

// تحسين الأداء - تأجيل تحميل الجافاسكريبت
function arabstudio_defer_scripts($tag, $handle, $src) {
    $defer_scripts = array('arabstudio-script', 'threejs', 'gsap');
    
    if (in_array($handle, $defer_scripts)) {
        return str_replace('<script ', '<script defer ', $tag);
    }
    
    return $tag;
}
add_filter('script_loader_tag', 'arabstudio_defer_scripts', 10, 3);

// إضافة أحجام صور مخصصة
function arabstudio_image_sizes() {
    add_image_size('hero-image', 1920, 1080, true);
    add_image_size('service-thumb', 400, 300, true);
    add_image_size('portfolio-thumb', 600, 400, true);
}
add_action('after_setup_theme', 'arabstudio_image_sizes');

// تحسين SEO - إضافة meta tags
function arabstudio_meta_tags() {
    if (is_home() || is_front_page()) {
        echo '<meta name="description" content="عرب استوديو - شركة رائدة في مجال الإعلان والتسويق الرقمي في البصرة، العراق. نقدم خدمات إعلانية متميزة وحلول تسويقية إبداعية.">' . "\n";
        echo '<meta name="keywords" content="عرب استوديو, إعلان, تسويق رقمي, البصرة, العراق, تصميم, برمجة">' . "\n";
        echo '<meta property="og:title" content="عرب استوديو - شركة الإعلان والتسويق الرقمي">' . "\n";
        echo '<meta property="og:description" content="شركة رائدة في مجال الإعلان والتسويق الرقمي في البصرة، العراق">' . "\n";
        echo '<meta property="og:type" content="website">' . "\n";
        echo '<meta property="og:url" content="' . home_url() . '">' . "\n";
    }
}
add_action('wp_head', 'arabstudio_meta_tags');

// تفعيل ضغط الملفات
function arabstudio_enable_compression() {
    if (!ob_get_level()) {
        ob_start('ob_gzhandler');
    }
}
add_action('init', 'arabstudio_enable_compression');

// إزالة الإصدار من CSS و JS لتحسين الأمان
function arabstudio_remove_version_strings($src) {
    if (strpos($src, 'ver=')) {
        $src = remove_query_arg('ver', $src);
    }
    return $src;
}
add_filter('style_loader_src', 'arabstudio_remove_version_strings', 9999);
add_filter('script_loader_src', 'arabstudio_remove_version_strings', 9999);

// إضافة دعم الترجمة
function arabstudio_load_textdomain() {
    load_theme_textdomain('arabstudio', get_template_directory() . '/languages');
}
add_action('after_setup_theme', 'arabstudio_load_textdomain');

// تخصيص طول المقتطف
function arabstudio_excerpt_length($length) {
    return 30;
}
add_filter('excerpt_length', 'arabstudio_excerpt_length', 999);

// تخصيص نهاية المقتطف
function arabstudio_excerpt_more($more) {
    return '...';
}
add_filter('excerpt_more', 'arabstudio_excerpt_more');

// إضافة فئات CSS للجسم
function arabstudio_body_classes($classes) {
    if (is_rtl()) {
        $classes[] = 'rtl';
    }
    
    if (is_home() || is_front_page()) {
        $classes[] = 'home-page';
    }
    
    return $classes;
}
add_filter('body_class', 'arabstudio_body_classes');

// تحسين الأمان - إزالة معلومات WordPress
remove_action('wp_head', 'wp_generator');
remove_action('wp_head', 'wlwmanifest_link');
remove_action('wp_head', 'rsd_link');

// تحسين الأداء - إزالة emoji scripts
function arabstudio_disable_emojis() {
    remove_action('wp_head', 'print_emoji_detection_script', 7);
    remove_action('admin_print_scripts', 'print_emoji_detection_script');
    remove_action('wp_print_styles', 'print_emoji_styles');
    remove_action('admin_print_styles', 'print_emoji_styles');
    remove_filter('the_content_feed', 'wp_staticize_emoji');
    remove_filter('comment_text_rss', 'wp_staticize_emoji');
    remove_filter('wp_mail', 'wp_staticize_emoji_for_email');
}
add_action('init', 'arabstudio_disable_emojis');

// تحسين قاعدة البيانات
function arabstudio_optimize_database() {
    // تنظيف المراجعات القديمة
    global $wpdb;
    $wpdb->query("DELETE FROM {$wpdb->posts} WHERE post_type = 'revision' AND post_date < DATE_SUB(NOW(), INTERVAL 30 DAY)");

    // تنظيف التعليقات المرفوضة
    $wpdb->query("DELETE FROM {$wpdb->comments} WHERE comment_approved = 'spam' AND comment_date < DATE_SUB(NOW(), INTERVAL 30 DAY)");

    // تحسين الجداول
    $wpdb->query("OPTIMIZE TABLE {$wpdb->posts}");
    $wpdb->query("OPTIMIZE TABLE {$wpdb->comments}");
    $wpdb->query("OPTIMIZE TABLE {$wpdb->options}");
}

// تشغيل تحسين قاعدة البيانات أسبوعياً
if (!wp_next_scheduled('arabstudio_weekly_cleanup')) {
    wp_schedule_event(time(), 'weekly', 'arabstudio_weekly_cleanup');
}
add_action('arabstudio_weekly_cleanup', 'arabstudio_optimize_database');

// تحسين الاستعلامات
function arabstudio_optimize_queries($query) {
    if (!is_admin() && $query->is_main_query()) {
        // تحديد عدد المقالات في الصفحة الرئيسية
        if ($query->is_home()) {
            $query->set('posts_per_page', 6);
        }

        // تحسين استعلامات البحث
        if ($query->is_search()) {
            $query->set('posts_per_page', 10);
            $query->set('post_type', array('post', 'page'));
        }
    }
}
add_action('pre_get_posts', 'arabstudio_optimize_queries');

// تفعيل WebP للصور
function arabstudio_enable_webp_upload($mimes) {
    $mimes['webp'] = 'image/webp';
    return $mimes;
}
add_filter('upload_mimes', 'arabstudio_enable_webp_upload');

// تحسين الصور تلقائياً
function arabstudio_optimize_images($metadata, $attachment_id) {
    if (!$metadata || !is_array($metadata)) {
        return $metadata;
    }

    $upload_dir = wp_upload_dir();
    $file_path = $upload_dir['basedir'] . '/' . $metadata['file'];

    // تحسين جودة JPEG
    if (strpos($metadata['file'], '.jpg') !== false || strpos($metadata['file'], '.jpeg') !== false) {
        $image = imagecreatefromjpeg($file_path);
        if ($image) {
            imagejpeg($image, $file_path, 85); // جودة 85%
            imagedestroy($image);
        }
    }

    return $metadata;
}
add_filter('wp_generate_attachment_metadata', 'arabstudio_optimize_images', 10, 2);

// إضافة Lazy Loading للصور
function arabstudio_add_lazy_loading($content) {
    if (is_admin() || is_feed() || is_preview()) {
        return $content;
    }

    // إضافة loading="lazy" للصور
    $content = preg_replace('/<img(.*?)src=/', '<img$1loading="lazy" src=', $content);

    return $content;
}
add_filter('the_content', 'arabstudio_add_lazy_loading');

// تحسين RSS Feed
function arabstudio_optimize_rss() {
    // إضافة الصور المميزة إلى RSS
    if (has_post_thumbnail()) {
        $thumbnail = get_the_post_thumbnail_url(get_the_ID(), 'medium');
        echo '<enclosure url="' . esc_url($thumbnail) . '" type="image/jpeg" />';
    }
}
add_action('rss2_item', 'arabstudio_optimize_rss');

// تحسين Sitemap
function arabstudio_sitemap_priority($priority, $type, $object_id) {
    if ($type === 'post') {
        // أولوية أعلى للمقالات الحديثة
        $post_date = get_the_date('U', $object_id);
        $days_old = (time() - $post_date) / (60 * 60 * 24);

        if ($days_old < 7) {
            return 1.0;
        } elseif ($days_old < 30) {
            return 0.8;
        } else {
            return 0.6;
        }
    }

    return $priority;
}
add_filter('wp_sitemaps_posts_entry', 'arabstudio_sitemap_priority', 10, 3);

// إضافة Schema.org Markup
function arabstudio_add_schema_markup() {
    if (is_single()) {
        global $post;
        $schema = array(
            '@context' => 'https://schema.org',
            '@type' => 'Article',
            'headline' => get_the_title(),
            'description' => get_the_excerpt(),
            'author' => array(
                '@type' => 'Person',
                'name' => get_the_author()
            ),
            'publisher' => array(
                '@type' => 'Organization',
                'name' => get_bloginfo('name'),
                'logo' => array(
                    '@type' => 'ImageObject',
                    'url' => get_template_directory_uri() . '/assets/images/logo.png'
                )
            ),
            'datePublished' => get_the_date('c'),
            'dateModified' => get_the_modified_date('c')
        );

        if (has_post_thumbnail()) {
            $schema['image'] = get_the_post_thumbnail_url(get_the_ID(), 'large');
        }

        echo '<script type="application/ld+json">' . json_encode($schema) . '</script>';
    }
}
add_action('wp_head', 'arabstudio_add_schema_markup');

// تحسين الأمان
function arabstudio_security_headers() {
    if (!is_admin()) {
        header('X-Content-Type-Options: nosniff');
        header('X-Frame-Options: SAMEORIGIN');
        header('X-XSS-Protection: 1; mode=block');
        header('Referrer-Policy: strict-origin-when-cross-origin');
    }
}
add_action('send_headers', 'arabstudio_security_headers');

// منع تعداد المستخدمين
function arabstudio_prevent_user_enumeration() {
    if (isset($_GET['author']) || preg_match('/\?author=([0-9]*)/i', $_SERVER['QUERY_STRING'])) {
        wp_redirect(home_url(), 301);
        exit;
    }
}
add_action('init', 'arabstudio_prevent_user_enumeration');

// تحسين تحميل الخطوط
function arabstudio_preload_fonts() {
    echo '<link rel="preload" href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700;800;900&display=swap" as="style" onload="this.onload=null;this.rel=\'stylesheet\'">';
    echo '<noscript><link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700;800;900&display=swap"></noscript>';
}
add_action('wp_head', 'arabstudio_preload_fonts', 1);

// إضافة Critical CSS
function arabstudio_critical_css() {
    if (is_front_page()) {
        echo '<style id="critical-css">
        body{font-family:Cairo,sans-serif;background:linear-gradient(135deg,#111827 0%,#1f2937 100%);color:#fff;margin:0;padding:0}
        .site-header{position:fixed;top:0;width:100%;background:rgba(17,24,39,.95);backdrop-filter:blur(20px);z-index:1000}
        .hero-section{height:100vh;display:flex;align-items:center;justify-content:center;text-align:center}
        .hero-title{font-size:4rem;font-weight:bold;background:linear-gradient(135deg,#dc2626 0%,#1f2937 100%);-webkit-background-clip:text;-webkit-text-fill-color:transparent}
        </style>';
    }
}
add_action('wp_head', 'arabstudio_critical_css', 1);

// تحسين تحميل JavaScript
function arabstudio_optimize_js_loading($tag, $handle, $src) {
    // تأجيل تحميل الجافاسكريبت غير الضروري
    $defer_scripts = array('arabstudio-script', 'threejs', 'gsap', 'jquery-migrate');

    if (in_array($handle, $defer_scripts)) {
        return str_replace('<script ', '<script defer ', $tag);
    }

    return $tag;
}
add_filter('script_loader_tag', 'arabstudio_optimize_js_loading', 10, 3);

// تحسين تحميل CSS
function arabstudio_optimize_css_loading($tag, $handle, $href, $media) {
    // تحميل CSS غير الضروري بشكل غير متزامن
    $async_styles = array('font-awesome');

    if (in_array($handle, $async_styles)) {
        return str_replace("rel='stylesheet'", "rel='preload' as='style' onload=\"this.onload=null;this.rel='stylesheet'\"", $tag);
    }

    return $tag;
}
add_filter('style_loader_tag', 'arabstudio_optimize_css_loading', 10, 4);
?>
