<!DOCTYPE html>
<html <?php language_attributes(); ?>>
<head>
    <meta charset="<?php bloginfo('charset'); ?>">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    
    <!-- Preconnect to external domains for performance -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link rel="preconnect" href="https://cdnjs.cloudflare.com">
    
    <!-- DNS prefetch for better performance -->
    <link rel="dns-prefetch" href="//fonts.googleapis.com">
    <link rel="dns-prefetch" href="//cdnjs.cloudflare.com">
    
    <!-- Theme color for mobile browsers -->
    <meta name="theme-color" content="#dc2626">
    <meta name="msapplication-TileColor" content="#dc2626">
    
    <!-- Open Graph meta tags -->
    <meta property="og:site_name" content="<?php bloginfo('name'); ?>">
    <meta property="og:locale" content="ar_IQ">
    
    <!-- Twitter Card meta tags -->
    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:site" content="@arabstudio">
    
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="<?php echo get_template_directory_uri(); ?>/assets/images/favicon.ico">
    <link rel="apple-touch-icon" sizes="180x180" href="<?php echo get_template_directory_uri(); ?>/assets/images/apple-touch-icon.png">
    
    <!-- Structured Data -->
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": "Organization",
        "name": "عرب استوديو",
        "alternateName": "Arab Studio",
        "url": "<?php echo home_url(); ?>",
        "logo": "<?php echo get_template_directory_uri(); ?>/assets/images/logo.png",
        "description": "شركة رائدة في مجال الإعلان والتسويق الرقمي في البصرة، العراق",
        "address": {
            "@type": "PostalAddress",
            "streetAddress": "الطويسة مقابل مركز شرطة الرباط",
            "addressLocality": "البصرة",
            "addressCountry": "العراق"
        },
        "contactPoint": {
            "@type": "ContactPoint",
            "contactType": "customer service",
            "areaServed": "IQ",
            "availableLanguage": ["Arabic", "English"]
        },
        "sameAs": [
            "https://www.facebook.com/arabstudio",
            "https://www.instagram.com/arabstudio",
            "https://www.linkedin.com/company/arabstudio"
        ]
    }
    </script>
    
    <?php wp_head(); ?>
</head>

<body <?php body_class(); ?>>
<?php wp_body_open(); ?>

<!-- Loading Screen -->
<div id="loading-screen" class="loading-screen">
    <div class="loading-content">
        <div class="loading-logo">
            <h1>عرب استوديو</h1>
        </div>
        <div class="loading-spinner">
            <div class="spinner"></div>
        </div>
        <p class="loading-text">جاري التحميل...</p>
    </div>
</div>

<!-- Skip to content link for accessibility -->
<a class="skip-link screen-reader-text" href="#main"><?php _e('Skip to content', 'arabstudio'); ?></a>

<!-- Header -->
<header class="site-header" id="masthead">
    <div class="header-container">
        <!-- Logo -->
        <div class="site-branding">
            <?php if (has_custom_logo()) : ?>
                <div class="site-logo">
                    <?php the_custom_logo(); ?>
                </div>
            <?php else : ?>
                <h1 class="site-title">
                    <a href="<?php echo esc_url(home_url('/')); ?>" class="logo" rel="home">
                        <?php bloginfo('name'); ?>
                    </a>
                </h1>
                <?php
                $description = get_bloginfo('description', 'display');
                if ($description || is_customize_preview()) :
                ?>
                    <p class="site-description"><?php echo $description; ?></p>
                <?php endif; ?>
            <?php endif; ?>
        </div>

        <!-- Navigation Menu -->
        <nav class="main-nav" id="site-navigation" aria-label="<?php esc_attr_e('Primary menu', 'arabstudio'); ?>">
            <button class="menu-toggle" aria-controls="primary-menu" aria-expanded="false">
                <span class="hamburger">
                    <span class="hamburger-line"></span>
                    <span class="hamburger-line"></span>
                    <span class="hamburger-line"></span>
                </span>
                <span class="menu-text"><?php _e('Menu', 'arabstudio'); ?></span>
            </button>
            
            <?php
            wp_nav_menu(array(
                'theme_location' => 'primary',
                'menu_id'        => 'primary-menu',
                'menu_class'     => 'nav-menu',
                'container'      => false,
                'fallback_cb'    => 'arabstudio_fallback_menu',
            ));
            ?>
        </nav>

        <!-- Header Actions -->
        <div class="header-actions">
            <!-- Language Switcher -->
            <div class="language-switcher">
                <button class="lang-toggle" aria-label="<?php _e('Switch Language', 'arabstudio'); ?>">
                    <i class="fas fa-globe"></i>
                    <span>العربية</span>
                </button>
            </div>
            
            <!-- Contact Button -->
            <a href="#contact" class="header-cta btn btn-outline">
                <i class="fas fa-phone"></i>
                <span><?php _e('Contact Us', 'arabstudio'); ?></span>
            </a>
        </div>
    </div>
</header>

<!-- Mobile Menu Overlay -->
<div class="mobile-menu-overlay" id="mobile-menu-overlay">
    <div class="mobile-menu-content">
        <button class="mobile-menu-close" aria-label="<?php _e('Close Menu', 'arabstudio'); ?>">
            <i class="fas fa-times"></i>
        </button>
        
        <?php
        wp_nav_menu(array(
            'theme_location' => 'primary',
            'menu_id'        => 'mobile-menu',
            'menu_class'     => 'mobile-nav-menu',
            'container'      => false,
            'fallback_cb'    => 'arabstudio_fallback_menu',
        ));
        ?>
        
        <div class="mobile-menu-footer">
            <div class="mobile-contact-info">
                <p><i class="fas fa-map-marker-alt"></i> البصرة - الطويسة</p>
                <p><i class="fas fa-phone"></i> +964 XXX XXX XXXX</p>
                <p><i class="fas fa-envelope"></i> <EMAIL></p>
            </div>
            
            <div class="mobile-social-links">
                <a href="#" aria-label="Facebook"><i class="fab fa-facebook-f"></i></a>
                <a href="#" aria-label="Instagram"><i class="fab fa-instagram"></i></a>
                <a href="#" aria-label="LinkedIn"><i class="fab fa-linkedin-in"></i></a>
                <a href="#" aria-label="Twitter"><i class="fab fa-twitter"></i></a>
            </div>
        </div>
    </div>
</div>

<?php
// Fallback menu function
function arabstudio_fallback_menu() {
    echo '<ul class="nav-menu fallback-menu">';
    echo '<li><a href="' . home_url('/') . '">' . __('Home', 'arabstudio') . '</a></li>';
    echo '<li><a href="#services">' . __('Services', 'arabstudio') . '</a></li>';
    echo '<li><a href="#about">' . __('About', 'arabstudio') . '</a></li>';
    echo '<li><a href="#contact">' . __('Contact', 'arabstudio') . '</a></li>';
    echo '</ul>';
}
?>
