/**
 * Arab Studio Theme - Main JavaScript File
 * ملف الجافاسكريبت الرئيسي لثيم عرب استوديو
 */

(function($) {
    'use strict';

    // متغيرات عامة
    let scene, camera, renderer, particles, heroCanvas, aboutCanvas;
    let mouseX = 0, mouseY = 0;
    let windowHalfX = window.innerWidth / 2;
    let windowHalfY = window.innerHeight / 2;

    // تهيئة الموقع عند التحميل
    $(document).ready(function() {
        initializeWebsite();
        initThreeJS();
        initAnimations();
        initEventListeners();
        initAOS();
    });

    // تهيئة الموقع
    function initializeWebsite() {
        // إخفاء شاشة التحميل
        setTimeout(function() {
            $('#loading-screen').addClass('hidden');
        }, 2000);

        // تفعيل التمرير السلس
        $('a[href*="#"]').on('click', function(e) {
            e.preventDefault();
            const target = $(this.getAttribute('href'));
            if (target.length) {
                $('html, body').animate({
                    scrollTop: target.offset().top - 80
                }, 1000);
            }
        });

        // تأثير الهيدر عند التمرير
        $(window).scroll(function() {
            const scrollTop = $(this).scrollTop();
            
            if (scrollTop > 100) {
                $('.site-header').addClass('scrolled');
                $('.back-to-top').addClass('visible');
            } else {
                $('.site-header').removeClass('scrolled');
                $('.back-to-top').removeClass('visible');
            }
        });

        // إظهار إشعار الكوكيز
        if (!localStorage.getItem('cookiesAccepted')) {
            setTimeout(function() {
                $('#cookieNotice').addClass('visible');
            }, 3000);
        }
    }

    // تهيئة Three.js للأنيميشن ثلاثي الأبعاد
    function initThreeJS() {
        // تهيئة كانفاس البطل
        heroCanvas = document.getElementById('hero-canvas');
        if (heroCanvas) {
            initHeroScene();
        }

        // تهيئة كانفاس قسم عن الشركة
        aboutCanvas = document.getElementById('about-canvas');
        if (aboutCanvas) {
            initAboutScene();
        }
    }

    // تهيئة مشهد البطل
    function initHeroScene() {
        // إنشاء المشهد
        scene = new THREE.Scene();
        
        // إنشاء الكاميرا
        camera = new THREE.PerspectiveCamera(75, heroCanvas.clientWidth / heroCanvas.clientHeight, 0.1, 1000);
        camera.position.z = 5;

        // إنشاء المرندر
        renderer = new THREE.WebGLRenderer({ 
            canvas: heroCanvas, 
            alpha: true,
            antialias: true 
        });
        renderer.setSize(heroCanvas.clientWidth, heroCanvas.clientHeight);
        renderer.setPixelRatio(Math.min(window.devicePixelRatio, 2));

        // إنشاء الجسيمات
        createParticles();

        // إنشاء الأشكال الهندسية
        createGeometricShapes();

        // بدء الأنيميشن
        animate();
    }

    // إنشاء الجسيمات
    function createParticles() {
        const particleCount = 1000;
        const positions = new Float32Array(particleCount * 3);
        const colors = new Float32Array(particleCount * 3);

        for (let i = 0; i < particleCount; i++) {
            positions[i * 3] = (Math.random() - 0.5) * 20;
            positions[i * 3 + 1] = (Math.random() - 0.5) * 20;
            positions[i * 3 + 2] = (Math.random() - 0.5) * 20;

            // ألوان الجسيمات (أحمر ورمادي)
            const color = Math.random() > 0.5 ? 
                new THREE.Color(0xdc2626) : 
                new THREE.Color(0x6b7280);
            
            colors[i * 3] = color.r;
            colors[i * 3 + 1] = color.g;
            colors[i * 3 + 2] = color.b;
        }

        const geometry = new THREE.BufferGeometry();
        geometry.setAttribute('position', new THREE.BufferAttribute(positions, 3));
        geometry.setAttribute('color', new THREE.BufferAttribute(colors, 3));

        const material = new THREE.PointsMaterial({
            size: 0.05,
            vertexColors: true,
            transparent: true,
            opacity: 0.8
        });

        particles = new THREE.Points(geometry, material);
        scene.add(particles);
    }

    // إنشاء الأشكال الهندسية
    function createGeometricShapes() {
        // إنشاء مكعبات دوارة
        const cubeGeometry = new THREE.BoxGeometry(0.5, 0.5, 0.5);
        const cubeMaterial = new THREE.MeshBasicMaterial({
            color: 0xdc2626,
            wireframe: true,
            transparent: true,
            opacity: 0.3
        });

        for (let i = 0; i < 5; i++) {
            const cube = new THREE.Mesh(cubeGeometry, cubeMaterial);
            cube.position.set(
                (Math.random() - 0.5) * 10,
                (Math.random() - 0.5) * 10,
                (Math.random() - 0.5) * 10
            );
            cube.rotation.set(
                Math.random() * Math.PI,
                Math.random() * Math.PI,
                Math.random() * Math.PI
            );
            scene.add(cube);
        }

        // إنشاء دوائر
        const ringGeometry = new THREE.RingGeometry(0.5, 1, 32);
        const ringMaterial = new THREE.MeshBasicMaterial({
            color: 0x6b7280,
            transparent: true,
            opacity: 0.2,
            side: THREE.DoubleSide
        });

        for (let i = 0; i < 3; i++) {
            const ring = new THREE.Mesh(ringGeometry, ringMaterial);
            ring.position.set(
                (Math.random() - 0.5) * 8,
                (Math.random() - 0.5) * 8,
                (Math.random() - 0.5) * 8
            );
            scene.add(ring);
        }
    }

    // تهيئة مشهد قسم عن الشركة
    function initAboutScene() {
        const aboutScene = new THREE.Scene();
        const aboutCamera = new THREE.PerspectiveCamera(75, aboutCanvas.clientWidth / aboutCanvas.clientHeight, 0.1, 1000);
        const aboutRenderer = new THREE.WebGLRenderer({ 
            canvas: aboutCanvas, 
            alpha: true,
            antialias: true 
        });
        
        aboutRenderer.setSize(aboutCanvas.clientWidth, aboutCanvas.clientHeight);
        aboutRenderer.setPixelRatio(Math.min(window.devicePixelRatio, 2));
        
        // إنشاء شكل هندسي معقد
        const geometry = new THREE.IcosahedronGeometry(2, 1);
        const material = new THREE.MeshBasicMaterial({
            color: 0xdc2626,
            wireframe: true,
            transparent: true,
            opacity: 0.6
        });
        
        const mesh = new THREE.Mesh(geometry, material);
        aboutScene.add(mesh);
        
        aboutCamera.position.z = 5;
        
        // أنيميشن المشهد
        function animateAbout() {
            requestAnimationFrame(animateAbout);
            
            mesh.rotation.x += 0.01;
            mesh.rotation.y += 0.01;
            
            aboutRenderer.render(aboutScene, aboutCamera);
        }
        
        animateAbout();
    }

    // حلقة الأنيميشن الرئيسية
    function animate() {
        requestAnimationFrame(animate);

        // تحريك الجسيمات
        if (particles) {
            particles.rotation.x += 0.001;
            particles.rotation.y += 0.002;
            
            // تأثير الماوس
            particles.rotation.x += (mouseY * 0.00005);
            particles.rotation.y += (mouseX * 0.00005);
        }

        // تحريك الأشكال الهندسية
        scene.children.forEach((child, index) => {
            if (child.type === 'Mesh') {
                child.rotation.x += 0.01 * (index + 1);
                child.rotation.y += 0.01 * (index + 1);
                child.rotation.z += 0.005 * (index + 1);
            }
        });

        // تحديث الكاميرا
        camera.position.x += (mouseX * 0.0001 - camera.position.x) * 0.05;
        camera.position.y += (-mouseY * 0.0001 - camera.position.y) * 0.05;
        camera.lookAt(scene.position);

        renderer.render(scene, camera);
    }

    // تهيئة الأنيميشن
    function initAnimations() {
        // أنيميشن العدادات
        $('.stat-number').each(function() {
            const $this = $(this);
            const countTo = parseInt($this.attr('data-count'));
            
            $({ countNum: 0 }).animate({
                countNum: countTo
            }, {
                duration: 3000,
                easing: 'swing',
                step: function() {
                    $this.text(Math.floor(this.countNum));
                },
                complete: function() {
                    $this.text(countTo);
                }
            });
        });

        // تأثيرات GSAP
        if (typeof gsap !== 'undefined') {
            // أنيميشن العنوان الرئيسي
            gsap.from('.hero-title', {
                duration: 1.5,
                y: 100,
                opacity: 0,
                ease: 'power3.out'
            });

            // أنيميشن بطاقات الخدمات
            gsap.from('.service-card', {
                duration: 1,
                y: 50,
                opacity: 0,
                stagger: 0.2,
                ease: 'power2.out',
                scrollTrigger: {
                    trigger: '.services-section',
                    start: 'top 80%'
                }
            });
        }
    }

    // تهيئة مستمعي الأحداث
    function initEventListeners() {
        // تتبع حركة الماوس
        $(document).mousemove(function(event) {
            mouseX = event.clientX - windowHalfX;
            mouseY = event.clientY - windowHalfY;
        });

        // تغيير حجم النافذة
        $(window).resize(function() {
            windowHalfX = window.innerWidth / 2;
            windowHalfY = window.innerHeight / 2;
            
            if (camera && renderer) {
                camera.aspect = window.innerWidth / window.innerHeight;
                camera.updateProjectionMatrix();
                renderer.setSize(window.innerWidth, window.innerHeight);
            }
        });

        // قائمة الهاتف المحمول
        $('.menu-toggle').click(function() {
            $(this).toggleClass('active');
            $('.mobile-menu-overlay').toggleClass('active');
        });

        $('.mobile-menu-close, .mobile-menu-overlay').click(function(e) {
            if (e.target === this) {
                $('.menu-toggle').removeClass('active');
                $('.mobile-menu-overlay').removeClass('active');
            }
        });

        // العودة إلى الأعلى
        $('.back-to-top').click(function() {
            $('html, body').animate({ scrollTop: 0 }, 1000);
        });

        // قبول الكوكيز
        $('#acceptCookies').click(function() {
            localStorage.setItem('cookiesAccepted', 'true');
            $('#cookieNotice').removeClass('visible');
        });

        // إرسال النموذج
        $('.contact-form-element').submit(function(e) {
            e.preventDefault();
            // هنا يمكن إضافة كود إرسال النموذج
            alert('تم إرسال رسالتك بنجاح! سنتواصل معك قريباً.');
        });

        // النشرة الإخبارية
        $('.newsletter-form').submit(function(e) {
            e.preventDefault();
            // هنا يمكن إضافة كود الاشتراك في النشرة
            alert('تم اشتراكك في النشرة الإخبارية بنجاح!');
        });
    }

    // تهيئة AOS (Animate On Scroll)
    function initAOS() {
        // تطبيق مبسط لـ AOS
        $(window).scroll(function() {
            $('[data-aos]').each(function() {
                const elementTop = $(this).offset().top;
                const elementBottom = elementTop + $(this).outerHeight();
                const viewportTop = $(window).scrollTop();
                const viewportBottom = viewportTop + $(window).height();

                if (elementBottom > viewportTop && elementTop < viewportBottom) {
                    $(this).addClass('aos-animate');
                }
            });
        });
    }

    // تأثيرات إضافية للأنيميشن
    function initAdvancedAnimations() {
        // تأثير الكتابة المتحركة
        if (typeof gsap !== 'undefined') {
            // أنيميشن النص المتحرك
            gsap.registerPlugin(TextPlugin);

            const heroTitle = document.querySelector('.hero-title');
            if (heroTitle) {
                const text = heroTitle.textContent;
                heroTitle.textContent = '';

                gsap.to(heroTitle, {
                    duration: 2,
                    text: text,
                    ease: "none",
                    delay: 0.5
                });
            }

            // أنيميشن الجسيمات المتطورة
            gsap.timeline({ repeat: -1, yoyo: true })
                .to('.animated-bg::before', {
                    duration: 10,
                    backgroundPosition: '200% 200%',
                    ease: 'sine.inOut'
                });

            // تأثير المغناطيس للأزرار
            document.querySelectorAll('.btn').forEach(btn => {
                btn.addEventListener('mouseenter', function(e) {
                    gsap.to(this, {
                        duration: 0.3,
                        scale: 1.05,
                        rotationY: 5,
                        transformPerspective: 500,
                        ease: 'power2.out'
                    });
                });

                btn.addEventListener('mouseleave', function(e) {
                    gsap.to(this, {
                        duration: 0.3,
                        scale: 1,
                        rotationY: 0,
                        ease: 'power2.out'
                    });
                });
            });

            // تأثير التمرير المتوازي
            gsap.registerPlugin(ScrollTrigger);

            gsap.utils.toArray('.service-card').forEach((card, i) => {
                gsap.fromTo(card, {
                    y: 100,
                    opacity: 0,
                    rotationX: -15
                }, {
                    y: 0,
                    opacity: 1,
                    rotationX: 0,
                    duration: 1,
                    delay: i * 0.1,
                    ease: 'power3.out',
                    scrollTrigger: {
                        trigger: card,
                        start: 'top 85%',
                        end: 'bottom 15%',
                        toggleActions: 'play none none reverse'
                    }
                });
            });

            // تأثير الأرقام المتحركة المتقدم
            gsap.utils.toArray('.stat-number').forEach(stat => {
                const endValue = parseInt(stat.getAttribute('data-count'));

                gsap.fromTo(stat, {
                    textContent: 0
                }, {
                    textContent: endValue,
                    duration: 2,
                    ease: 'power2.out',
                    snap: { textContent: 1 },
                    scrollTrigger: {
                        trigger: stat,
                        start: 'top 80%'
                    }
                });
            });
        }
    }

    // تأثيرات الماوس المتقدمة
    function initMouseEffects() {
        // تأثير المؤشر المخصص
        const cursor = document.createElement('div');
        cursor.className = 'custom-cursor';
        document.body.appendChild(cursor);

        const cursorFollower = document.createElement('div');
        cursorFollower.className = 'cursor-follower';
        document.body.appendChild(cursorFollower);

        let mouseX = 0, mouseY = 0;
        let followerX = 0, followerY = 0;

        document.addEventListener('mousemove', (e) => {
            mouseX = e.clientX;
            mouseY = e.clientY;

            cursor.style.left = mouseX + 'px';
            cursor.style.top = mouseY + 'px';
        });

        // تحريك المتابع بسلاسة
        function updateFollower() {
            followerX += (mouseX - followerX) * 0.1;
            followerY += (mouseY - followerY) * 0.1;

            cursorFollower.style.left = followerX + 'px';
            cursorFollower.style.top = followerY + 'px';

            requestAnimationFrame(updateFollower);
        }
        updateFollower();

        // تأثيرات عند التمرير على العناصر
        document.querySelectorAll('a, button, .service-card').forEach(el => {
            el.addEventListener('mouseenter', () => {
                cursor.classList.add('cursor-hover');
                cursorFollower.classList.add('cursor-hover');
            });

            el.addEventListener('mouseleave', () => {
                cursor.classList.remove('cursor-hover');
                cursorFollower.classList.remove('cursor-hover');
            });
        });
    }

    // تأثيرات الصوت (اختيارية)
    function initSoundEffects() {
        // يمكن إضافة تأثيرات صوتية للتفاعلات
        const playSound = (soundType) => {
            // تشغيل الأصوات عند التفاعل
            if ('AudioContext' in window) {
                const audioContext = new AudioContext();
                const oscillator = audioContext.createOscillator();
                const gainNode = audioContext.createGain();

                oscillator.connect(gainNode);
                gainNode.connect(audioContext.destination);

                oscillator.frequency.setValueAtTime(soundType === 'click' ? 800 : 400, audioContext.currentTime);
                gainNode.gain.setValueAtTime(0.1, audioContext.currentTime);
                gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.1);

                oscillator.start(audioContext.currentTime);
                oscillator.stop(audioContext.currentTime + 0.1);
            }
        };

        // إضافة الأصوات للأزرار
        document.querySelectorAll('.btn').forEach(btn => {
            btn.addEventListener('click', () => playSound('click'));
        });
    }

    // تحسين الأداء
    function optimizePerformance() {
        // تأجيل تحميل الصور
        const images = document.querySelectorAll('img[data-src]');
        const imageObserver = new IntersectionObserver((entries, observer) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    const img = entry.target;
                    img.src = img.dataset.src;
                    img.classList.remove('lazy');
                    observer.unobserve(img);
                }
            });
        });

        images.forEach(img => imageObserver.observe(img));

        // تحسين الأنيميشن للأجهزة الضعيفة
        const isLowPerformance = navigator.hardwareConcurrency < 4 ||
                                navigator.deviceMemory < 4;

        if (isLowPerformance) {
            document.body.classList.add('low-performance');
            // تقليل جودة الأنيميشن
            if (renderer) {
                renderer.setPixelRatio(1);
            }
        }

        // تحسين التمرير
        let ticking = false;
        function updateScrollEffects() {
            // تحديث التأثيرات المرتبطة بالتمرير
            ticking = false;
        }

        window.addEventListener('scroll', () => {
            if (!ticking) {
                requestAnimationFrame(updateScrollEffects);
                ticking = true;
            }
        });
    }

    // تهيئة جميع التأثيرات المتقدمة
    function initAllAdvancedFeatures() {
        initAdvancedAnimations();
        initMouseEffects();
        // initSoundEffects(); // يمكن تفعيلها حسب الحاجة
        optimizePerformance();
    }

    // تشغيل التأثيرات المتقدمة بعد تحميل الصفحة
    $(window).on('load', function() {
        setTimeout(initAllAdvancedFeatures, 1000);
    });

})(jQuery);
