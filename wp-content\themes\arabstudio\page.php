<?php
/**
 * صفحة عامة - Arab Studio Theme
 * Page Template
 */

get_header(); ?>

<div class="animated-bg"></div>

<main class="main-content page-content">
    <div class="container">
        <?php while (have_posts()) : the_post(); ?>
            <article id="page-<?php the_ID(); ?>" <?php post_class('page-article'); ?>>
                
                <!-- عنوان الصفحة -->
                <header class="page-header">
                    <h1 class="page-title"><?php the_title(); ?></h1>
                    
                    <?php if (has_excerpt()) : ?>
                        <div class="page-excerpt">
                            <?php the_excerpt(); ?>
                        </div>
                    <?php endif; ?>
                </header>

                <!-- الصورة المميزة -->
                <?php if (has_post_thumbnail()) : ?>
                    <div class="page-featured-image">
                        <?php the_post_thumbnail('large', array('class' => 'featured-img')); ?>
                    </div>
                <?php endif; ?>

                <!-- محتوى الصفحة -->
                <div class="page-content-area">
                    <?php 
                    the_content();
                    
                    wp_link_pages(array(
                        'before' => '<div class="page-links">' . __('Pages:', 'arabstudio'),
                        'after'  => '</div>',
                    ));
                    ?>
                </div>

                <!-- معلومات إضافية للصفحة -->
                <?php if (is_page('contact') || is_page('تواصل-معنا')) : ?>
                    <div class="contact-info-section">
                        <div class="contact-grid">
                            <div class="contact-item">
                                <div class="contact-icon">
                                    <i class="fas fa-map-marker-alt"></i>
                                </div>
                                <div class="contact-details">
                                    <h4><?php _e('Address', 'arabstudio'); ?></h4>
                                    <p>البصرة - الطويسة<br>مقابل مركز شرطة الرباط</p>
                                </div>
                            </div>
                            
                            <div class="contact-item">
                                <div class="contact-icon">
                                    <i class="fas fa-phone"></i>
                                </div>
                                <div class="contact-details">
                                    <h4><?php _e('Phone', 'arabstudio'); ?></h4>
                                    <p><a href="tel:+964XXXXXXXXX">+964 XXX XXX XXXX</a></p>
                                </div>
                            </div>
                            
                            <div class="contact-item">
                                <div class="contact-icon">
                                    <i class="fas fa-envelope"></i>
                                </div>
                                <div class="contact-details">
                                    <h4><?php _e('Email', 'arabstudio'); ?></h4>
                                    <p><a href="mailto:<EMAIL>"><EMAIL></a></p>
                                </div>
                            </div>
                            
                            <div class="contact-item">
                                <div class="contact-icon">
                                    <i class="fas fa-clock"></i>
                                </div>
                                <div class="contact-details">
                                    <h4><?php _e('Working Hours', 'arabstudio'); ?></h4>
                                    <p>الأحد - الخميس: 9:00 ص - 6:00 م</p>
                                </div>
                            </div>
                        </div>
                    </div>
                <?php endif; ?>

                <!-- نموذج التواصل للصفحات ذات الصلة -->
                <?php if (is_page('contact') || is_page('تواصل-معنا')) : ?>
                    <div class="page-contact-form">
                        <h3><?php _e('Send us a message', 'arabstudio'); ?></h3>
                        <form class="contact-form-element" method="post" action="">
                            <div class="form-row">
                                <div class="form-group">
                                    <input type="text" name="name" placeholder="<?php _e('Full Name', 'arabstudio'); ?>" required>
                                </div>
                                <div class="form-group">
                                    <input type="email" name="email" placeholder="<?php _e('Email Address', 'arabstudio'); ?>" required>
                                </div>
                            </div>
                            
                            <div class="form-row">
                                <div class="form-group">
                                    <input type="tel" name="phone" placeholder="<?php _e('Phone Number', 'arabstudio'); ?>">
                                </div>
                                <div class="form-group">
                                    <select name="service" required>
                                        <option value=""><?php _e('Select Service', 'arabstudio'); ?></option>
                                        <option value="digital-advertising"><?php _e('Digital Advertising', 'arabstudio'); ?></option>
                                        <option value="creative-design"><?php _e('Creative Design', 'arabstudio'); ?></option>
                                        <option value="strategic-marketing"><?php _e('Strategic Marketing', 'arabstudio'); ?></option>
                                        <option value="content-production"><?php _e('Content Production', 'arabstudio'); ?></option>
                                        <option value="mobile-apps"><?php _e('Mobile Apps', 'arabstudio'); ?></option>
                                        <option value="social-media"><?php _e('Social Media Management', 'arabstudio'); ?></option>
                                    </select>
                                </div>
                            </div>
                            
                            <div class="form-group">
                                <textarea name="message" placeholder="<?php _e('Your Message', 'arabstudio'); ?>" rows="5" required></textarea>
                            </div>
                            
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-paper-plane"></i>
                                <?php _e('Send Message', 'arabstudio'); ?>
                            </button>
                        </form>
                    </div>
                <?php endif; ?>

            </article>
        <?php endwhile; ?>
    </div>
</main>

<style>
/* أنماط الصفحات العامة */
.page-content {
    padding: 6rem 0 4rem;
    min-height: 100vh;
}

.page-article {
    max-width: 1000px;
    margin: 0 auto;
}

.page-header {
    text-align: center;
    margin-bottom: 3rem;
    padding: 3rem;
    background: rgba(31, 41, 55, 0.8);
    border-radius: 20px;
    border: 1px solid rgba(220, 38, 38, 0.1);
}

.page-title {
    font-size: 3rem;
    font-weight: bold;
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin-bottom: 1rem;
    line-height: 1.2;
}

.page-excerpt {
    font-size: 1.2rem;
    color: var(--light-gray);
    max-width: 600px;
    margin: 0 auto;
    line-height: 1.6;
}

.page-featured-image {
    margin: 3rem 0;
    text-align: center;
}

.featured-img {
    max-width: 100%;
    height: auto;
    border-radius: 20px;
    box-shadow: var(--shadow-luxury);
}

.page-content-area {
    background: rgba(31, 41, 55, 0.8);
    padding: 3rem;
    border-radius: 20px;
    border: 1px solid rgba(220, 38, 38, 0.1);
    margin-bottom: 3rem;
    color: var(--light-gray);
    line-height: 1.8;
    font-size: 1.1rem;
}

.page-content-area h2,
.page-content-area h3,
.page-content-area h4 {
    color: var(--white);
    margin: 2rem 0 1rem;
}

.page-content-area h2 {
    font-size: 2rem;
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.page-content-area p {
    margin-bottom: 1.5rem;
}

.page-content-area ul,
.page-content-area ol {
    margin: 1.5rem 0;
    padding-right: 2rem;
}

.page-content-area li {
    margin-bottom: 0.5rem;
}

/* معلومات التواصل */
.contact-info-section {
    background: rgba(31, 41, 55, 0.8);
    padding: 3rem;
    border-radius: 20px;
    border: 1px solid rgba(220, 38, 38, 0.1);
    margin-bottom: 3rem;
}

.contact-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
}

.contact-item {
    display: flex;
    align-items: flex-start;
    gap: 1rem;
    padding: 1.5rem;
    background: rgba(17, 24, 39, 0.8);
    border-radius: 15px;
    transition: all 0.3s ease;
}

.contact-item:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-glow);
}

.contact-icon {
    width: 50px;
    height: 50px;
    background: var(--gradient-primary);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--white);
    font-size: 1.2rem;
    flex-shrink: 0;
}

.contact-details h4 {
    color: var(--white);
    font-size: 1.2rem;
    margin-bottom: 0.5rem;
}

.contact-details p {
    color: var(--light-gray);
    line-height: 1.6;
    margin: 0;
}

.contact-details a {
    color: var(--primary-red);
    text-decoration: none;
    transition: all 0.3s ease;
}

.contact-details a:hover {
    color: var(--white);
}

/* نموذج التواصل في الصفحة */
.page-contact-form {
    background: rgba(31, 41, 55, 0.8);
    padding: 3rem;
    border-radius: 20px;
    border: 1px solid rgba(220, 38, 38, 0.1);
}

.page-contact-form h3 {
    color: var(--white);
    font-size: 1.8rem;
    margin-bottom: 2rem;
    text-align: center;
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
    margin-bottom: 1.5rem;
}

.form-group {
    margin-bottom: 1.5rem;
}

.form-group input,
.form-group select,
.form-group textarea {
    width: 100%;
    padding: 1rem;
    background: rgba(17, 24, 39, 0.8);
    border: 1px solid rgba(107, 114, 128, 0.3);
    border-radius: 10px;
    color: var(--white);
    font-size: 1rem;
    transition: all 0.3s ease;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: var(--primary-red);
    box-shadow: 0 0 0 3px rgba(220, 38, 38, 0.1);
}

.form-group input::placeholder,
.form-group textarea::placeholder {
    color: var(--light-gray);
}

.page-links {
    margin: 2rem 0;
    text-align: center;
}

.page-links a {
    display: inline-block;
    padding: 0.5rem 1rem;
    margin: 0 0.25rem;
    background: var(--gradient-primary);
    color: var(--white);
    text-decoration: none;
    border-radius: 5px;
    transition: all 0.3s ease;
}

.page-links a:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-glow);
}

/* تجاوب الشاشات */
@media (max-width: 768px) {
    .page-header,
    .page-content-area,
    .contact-info-section,
    .page-contact-form {
        padding: 2rem 1rem;
        margin: 0 1rem 2rem;
    }
    
    .page-title {
        font-size: 2.5rem;
    }
    
    .contact-grid {
        grid-template-columns: 1fr;
    }
    
    .form-row {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 480px) {
    .page-title {
        font-size: 2rem;
    }
    
    .page-header,
    .page-content-area,
    .contact-info-section,
    .page-contact-form {
        padding: 1.5rem;
    }
}
</style>

<?php get_footer(); ?>
