/*
 * ملف التجاوب المتقدم لثيم عرب استوديو
 * Advanced Responsive CSS for Arab Studio Theme
 * 
 * يحتوي على تحسينات التجاوب لجميع الأجهزة والشاشات
 */

/* ===== نقاط الكسر الأساسية ===== */
/* 
 * xs: 0-575px (الهواتف الصغيرة)
 * sm: 576-767px (الهواتف الكبيرة)
 * md: 768-991px (الأجهزة اللوحية)
 * lg: 992-1199px (أجهزة الكمبيوتر الصغيرة)
 * xl: 1200-1399px (أجهزة الكمبيوتر الكبيرة)
 * xxl: 1400px+ (الشاشات الكبيرة جداً)
 */

/* ===== الشاشات الكبيرة جداً (1400px+) ===== */
@media (min-width: 1400px) {
    .container {
        max-width: 1320px;
    }
    
    .hero-title {
        font-size: 5rem;
    }
    
    .section-title {
        font-size: 3.5rem;
    }
    
    .services-grid {
        grid-template-columns: repeat(3, 1fr);
    }
    
    .service-card {
        padding: 3rem;
    }
    
    .hero-canvas,
    .about-canvas {
        height: 500px;
    }
}

/* ===== الشاشات الكبيرة (1200-1399px) ===== */
@media (min-width: 1200px) and (max-width: 1399px) {
    .container {
        max-width: 1140px;
    }
    
    .hero-title {
        font-size: 4.5rem;
    }
    
    .section-title {
        font-size: 3.2rem;
    }
    
    .services-grid {
        grid-template-columns: repeat(3, 1fr);
    }
}

/* ===== أجهزة الكمبيوتر الصغيرة (992-1199px) ===== */
@media (min-width: 992px) and (max-width: 1199px) {
    .container {
        max-width: 960px;
    }
    
    .hero-title {
        font-size: 4rem;
    }
    
    .section-title {
        font-size: 3rem;
    }
    
    .services-grid {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .about-content,
    .contact-content {
        gap: 3rem;
    }
    
    .newsletter-content {
        flex-direction: column;
        gap: 2rem;
        text-align: center;
    }
}

/* ===== الأجهزة اللوحية (768-991px) ===== */
@media (min-width: 768px) and (max-width: 991px) {
    .container {
        max-width: 720px;
        padding: 0 1.5rem;
    }
    
    .hero-title {
        font-size: 3.5rem;
    }
    
    .hero-subtitle {
        font-size: 1.3rem;
    }
    
    .section-title {
        font-size: 2.8rem;
    }
    
    .services-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 1.5rem;
    }
    
    .service-card {
        padding: 2rem;
    }
    
    .about-content,
    .contact-content {
        grid-template-columns: 1fr;
        gap: 3rem;
    }
    
    .about-text,
    .contact-info {
        padding-right: 0;
        text-align: center;
    }
    
    .company-stats {
        justify-content: center;
    }
    
    .header-container {
        padding: 1rem 1.5rem;
    }
    
    .main-nav {
        display: none;
    }
    
    .menu-toggle {
        display: flex;
    }
    
    .hero-buttons {
        flex-direction: column;
        align-items: center;
        gap: 1rem;
    }
    
    .btn {
        width: 100%;
        max-width: 300px;
    }
    
    .footer-content {
        grid-template-columns: repeat(2, 1fr);
        gap: 2rem;
    }
    
    .newsletter-form .form-group {
        flex-direction: column;
        gap: 1rem;
    }
    
    .newsletter-form input {
        width: 100%;
    }
    
    .newsletter-form button {
        width: 100%;
    }
}

/* ===== الهواتف الكبيرة (576-767px) ===== */
@media (min-width: 576px) and (max-width: 767px) {
    .container {
        max-width: 540px;
        padding: 0 1rem;
    }
    
    .hero-title {
        font-size: 3rem;
    }
    
    .hero-subtitle {
        font-size: 1.2rem;
    }
    
    .section-title {
        font-size: 2.5rem;
    }
    
    .services-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }
    
    .service-card {
        padding: 2rem 1.5rem;
    }
    
    .service-icon {
        width: 70px;
        height: 70px;
        font-size: 1.8rem;
    }
    
    .company-stats {
        flex-direction: column;
        gap: 1.5rem;
        text-align: center;
    }
    
    .stat-number {
        font-size: 2.5rem;
    }
    
    .contact-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }
    
    .contact-item {
        padding: 1.5rem;
    }
    
    .footer-content {
        grid-template-columns: 1fr;
        gap: 2rem;
        text-align: center;
    }
    
    .footer-bottom-content {
        flex-direction: column;
        gap: 1rem;
        text-align: center;
    }
    
    .social-links {
        justify-content: center;
    }
    
    .whatsapp-float {
        bottom: 5rem;
        left: 1rem;
    }
    
    .whatsapp-float a {
        padding: 0.75rem 1rem;
    }
    
    .back-to-top {
        bottom: 5rem;
        right: 1rem;
        width: 45px;
        height: 45px;
    }
    
    .cookie-content {
        flex-direction: column;
        gap: 1rem;
        text-align: center;
    }
    
    .cookie-actions {
        justify-content: center;
    }
}

/* ===== الهواتف الصغيرة (0-575px) ===== */
@media (max-width: 575px) {
    .container {
        padding: 0 1rem;
    }
    
    .hero-title {
        font-size: 2.5rem;
        line-height: 1.1;
    }
    
    .hero-subtitle {
        font-size: 1.1rem;
    }
    
    .hero-description {
        font-size: 1rem;
    }
    
    .section-title {
        font-size: 2rem;
    }
    
    .section-subtitle {
        font-size: 1rem;
    }
    
    .services-section,
    .about-section,
    .contact-section {
        padding: 3rem 0;
    }
    
    .service-card {
        padding: 1.5rem;
        margin: 0 0.5rem;
    }
    
    .service-icon {
        width: 60px;
        height: 60px;
        font-size: 1.5rem;
    }
    
    .service-title {
        font-size: 1.3rem;
    }
    
    .service-description {
        font-size: 0.95rem;
    }
    
    .about-text {
        padding: 0;
    }
    
    .about-description {
        font-size: 1rem;
    }
    
    .company-stats {
        flex-direction: column;
        gap: 1rem;
    }
    
    .stat-number {
        font-size: 2rem;
    }
    
    .stat-label {
        font-size: 0.85rem;
    }
    
    .contact-form {
        padding: 1.5rem;
        margin: 0 0.5rem;
    }
    
    .form-group input,
    .form-group select,
    .form-group textarea {
        padding: 0.875rem;
        font-size: 0.95rem;
    }
    
    .btn {
        padding: 0.875rem 1.5rem;
        font-size: 0.95rem;
    }
    
    .header-container {
        padding: 0.75rem 1rem;
    }
    
    .logo {
        font-size: 1.5rem;
    }
    
    .site-header {
        padding: 0.5rem 0;
    }
    
    .main-content {
        padding-top: 70px;
    }
    
    .hero-section {
        padding: 2rem 0;
        min-height: 80vh;
    }
    
    .hero-content {
        padding: 0 1rem;
    }
    
    .hero-buttons {
        flex-direction: column;
        gap: 0.75rem;
    }
    
    .footer-main {
        padding: 2rem 0;
    }
    
    .footer-section {
        margin-bottom: 2rem;
    }
    
    .footer-title {
        font-size: 1.2rem;
    }
    
    .company-description {
        font-size: 0.9rem;
    }
    
    .social-links {
        gap: 0.75rem;
    }
    
    .social-link {
        width: 40px;
        height: 40px;
        font-size: 1rem;
    }
    
    .newsletter-section {
        padding: 2rem 0;
    }
    
    .newsletter-text h4 {
        font-size: 1.2rem;
    }
    
    .newsletter-text p {
        font-size: 0.9rem;
    }
    
    .whatsapp-float {
        bottom: 4rem;
        left: 0.5rem;
    }
    
    .whatsapp-float a {
        padding: 0.75rem;
        border-radius: 50%;
        min-width: 50px;
        min-height: 50px;
    }
    
    .whatsapp-text {
        display: none;
    }
    
    .back-to-top {
        bottom: 4rem;
        right: 0.5rem;
        width: 40px;
        height: 40px;
        font-size: 1rem;
    }
    
    .mobile-menu-content {
        padding: 1rem;
    }
    
    .mobile-nav-menu a {
        font-size: 1.3rem;
    }
    
    .mobile-contact-info p {
        font-size: 0.85rem;
    }
    
    .mobile-social-links a {
        width: 35px;
        height: 35px;
        font-size: 0.9rem;
    }
}

/* ===== الهواتف الصغيرة جداً (0-320px) ===== */
@media (max-width: 320px) {
    .hero-title {
        font-size: 2rem;
    }
    
    .section-title {
        font-size: 1.8rem;
    }
    
    .service-card {
        padding: 1rem;
    }
    
    .service-icon {
        width: 50px;
        height: 50px;
        font-size: 1.2rem;
    }
    
    .btn {
        padding: 0.75rem 1rem;
        font-size: 0.9rem;
    }
    
    .contact-form {
        padding: 1rem;
    }
    
    .form-group input,
    .form-group select,
    .form-group textarea {
        padding: 0.75rem;
        font-size: 0.9rem;
    }
    
    .whatsapp-float a,
    .back-to-top {
        width: 35px;
        height: 35px;
        font-size: 0.9rem;
    }
}

/* ===== تحسينات للشاشات عالية الكثافة ===== */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
    .hero-canvas,
    .about-canvas {
        image-rendering: -webkit-optimize-contrast;
        image-rendering: crisp-edges;
    }
    
    .custom-cursor,
    .cursor-follower {
        transform: scale(0.5);
        transform-origin: top left;
    }
}

/* ===== تحسينات للشاشات اللمسية ===== */
@media (hover: none) and (pointer: coarse) {
    .btn:hover,
    .service-card:hover,
    .social-link:hover {
        transform: none;
    }
    
    .btn:active,
    .service-card:active {
        transform: scale(0.98);
    }
    
    .custom-cursor,
    .cursor-follower {
        display: none;
    }
    
    /* زيادة حجم المناطق القابلة للنقر */
    .btn,
    .social-link,
    .contact-option {
        min-height: 44px;
        min-width: 44px;
    }
}

/* ===== تحسينات للطباعة ===== */
@media print {
    .animated-bg,
    .hero-canvas,
    .about-canvas,
    .loading-screen,
    .back-to-top,
    .whatsapp-float,
    .cookie-notice,
    .mobile-menu-overlay {
        display: none !important;
    }
    
    body {
        background: white !important;
        color: black !important;
        font-size: 12pt;
        line-height: 1.4;
    }
    
    .hero-section {
        height: auto !important;
        padding: 1rem 0 !important;
    }
    
    .hero-title,
    .section-title {
        color: black !important;
        background: none !important;
        -webkit-text-fill-color: black !important;
    }
    
    .container {
        max-width: none !important;
        padding: 0 !important;
    }
    
    .services-grid,
    .contact-content,
    .about-content {
        grid-template-columns: 1fr !important;
        gap: 1rem !important;
    }
    
    .service-card,
    .contact-form,
    .about-text {
        background: white !important;
        border: 1px solid #ccc !important;
        padding: 1rem !important;
        margin-bottom: 1rem !important;
    }
    
    a {
        color: black !important;
        text-decoration: underline !important;
    }
    
    .btn {
        background: white !important;
        color: black !important;
        border: 1px solid black !important;
    }
}

/* ===== تحسينات لتوفير البيانات ===== */
@media (prefers-reduced-data: reduce) {
    .hero-canvas,
    .about-canvas,
    .animated-bg::before {
        display: none;
    }
    
    .particle,
    .hologram-effect,
    .morphing-shape {
        animation: none;
    }
    
    img {
        loading: lazy;
    }
}

/* ===== تحسينات للحركة المخفضة ===== */
@media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
        scroll-behavior: auto !important;
    }
    
    .hero-canvas,
    .about-canvas {
        display: none;
    }
}

/* ===== تحسينات للوضع الداكن ===== */
@media (prefers-color-scheme: dark) {
    /* الثيم داكن بالفعل، لكن يمكن إضافة تحسينات إضافية */
    :root {
        --darker-gray: #000000;
        --shadow-luxury: 0 25px 50px -12px rgba(0, 0, 0, 0.9);
    }
}

/* ===== تحسينات للوضع الفاتح (إذا طُلب) ===== */
@media (prefers-color-scheme: light) {
    /* يمكن إضافة نسخة فاتحة من الثيم هنا إذا لزم الأمر */
}
