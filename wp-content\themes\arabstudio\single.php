<?php
/**
 * صفحة المقال المفرد - Arab Studio Theme
 * Single Post Template
 */

get_header(); ?>

<div class="animated-bg"></div>

<main class="main-content single-post">
    <div class="container">
        <?php while (have_posts()) : the_post(); ?>
            <article id="post-<?php the_ID(); ?>" <?php post_class('single-article'); ?>>
                
                <!-- عنوان المقال -->
                <header class="article-header">
                    <h1 class="article-title"><?php the_title(); ?></h1>
                    
                    <div class="article-meta">
                        <div class="meta-item">
                            <i class="fas fa-calendar-alt"></i>
                            <span><?php echo get_the_date(); ?></span>
                        </div>
                        
                        <div class="meta-item">
                            <i class="fas fa-user"></i>
                            <span><?php the_author(); ?></span>
                        </div>
                        
                        <?php if (has_category()) : ?>
                            <div class="meta-item">
                                <i class="fas fa-folder"></i>
                                <span><?php the_category(', '); ?></span>
                            </div>
                        <?php endif; ?>
                        
                        <?php if (has_tag()) : ?>
                            <div class="meta-item">
                                <i class="fas fa-tags"></i>
                                <span><?php the_tags('', ', '); ?></span>
                            </div>
                        <?php endif; ?>
                    </div>
                </header>

                <!-- الصورة المميزة -->
                <?php if (has_post_thumbnail()) : ?>
                    <div class="article-featured-image">
                        <?php the_post_thumbnail('large', array('class' => 'featured-img')); ?>
                    </div>
                <?php endif; ?>

                <!-- محتوى المقال -->
                <div class="article-content">
                    <?php the_content(); ?>
                    
                    <?php
                    wp_link_pages(array(
                        'before' => '<div class="page-links">' . __('Pages:', 'arabstudio'),
                        'after'  => '</div>',
                    ));
                    ?>
                </div>

                <!-- مشاركة المقال -->
                <div class="article-share">
                    <h4><?php _e('Share this article', 'arabstudio'); ?></h4>
                    <div class="share-buttons">
                        <a href="https://www.facebook.com/sharer/sharer.php?u=<?php echo urlencode(get_permalink()); ?>" 
                           target="_blank" class="share-btn facebook">
                            <i class="fab fa-facebook-f"></i>
                            <span>Facebook</span>
                        </a>
                        
                        <a href="https://twitter.com/intent/tweet?url=<?php echo urlencode(get_permalink()); ?>&text=<?php echo urlencode(get_the_title()); ?>" 
                           target="_blank" class="share-btn twitter">
                            <i class="fab fa-twitter"></i>
                            <span>Twitter</span>
                        </a>
                        
                        <a href="https://www.linkedin.com/sharing/share-offsite/?url=<?php echo urlencode(get_permalink()); ?>" 
                           target="_blank" class="share-btn linkedin">
                            <i class="fab fa-linkedin-in"></i>
                            <span>LinkedIn</span>
                        </a>
                        
                        <a href="https://wa.me/?text=<?php echo urlencode(get_the_title() . ' - ' . get_permalink()); ?>" 
                           target="_blank" class="share-btn whatsapp">
                            <i class="fab fa-whatsapp"></i>
                            <span>WhatsApp</span>
                        </a>
                    </div>
                </div>

                <!-- التنقل بين المقالات -->
                <nav class="post-navigation">
                    <div class="nav-links">
                        <?php
                        $prev_post = get_previous_post();
                        $next_post = get_next_post();
                        ?>
                        
                        <?php if ($prev_post) : ?>
                            <div class="nav-previous">
                                <a href="<?php echo get_permalink($prev_post); ?>" class="nav-link">
                                    <i class="fas fa-chevron-right"></i>
                                    <div class="nav-content">
                                        <span class="nav-label"><?php _e('Previous Article', 'arabstudio'); ?></span>
                                        <span class="nav-title"><?php echo get_the_title($prev_post); ?></span>
                                    </div>
                                </a>
                            </div>
                        <?php endif; ?>
                        
                        <?php if ($next_post) : ?>
                            <div class="nav-next">
                                <a href="<?php echo get_permalink($next_post); ?>" class="nav-link">
                                    <div class="nav-content">
                                        <span class="nav-label"><?php _e('Next Article', 'arabstudio'); ?></span>
                                        <span class="nav-title"><?php echo get_the_title($next_post); ?></span>
                                    </div>
                                    <i class="fas fa-chevron-left"></i>
                                </a>
                            </div>
                        <?php endif; ?>
                    </div>
                </nav>

            </article>

            <!-- التعليقات -->
            <?php
            if (comments_open() || get_comments_number()) :
                comments_template();
            endif;
            ?>

        <?php endwhile; ?>
    </div>
</main>

<style>
/* أنماط صفحة المقال المفرد */
.single-post {
    padding: 6rem 0 4rem;
    min-height: 100vh;
}

.single-article {
    max-width: 800px;
    margin: 0 auto;
    background: rgba(31, 41, 55, 0.8);
    border-radius: 20px;
    padding: 3rem;
    border: 1px solid rgba(220, 38, 38, 0.1);
}

.article-header {
    text-align: center;
    margin-bottom: 2rem;
    padding-bottom: 2rem;
    border-bottom: 1px solid rgba(107, 114, 128, 0.2);
}

.article-title {
    font-size: 2.5rem;
    font-weight: bold;
    color: var(--white);
    margin-bottom: 1rem;
    line-height: 1.2;
}

.article-meta {
    display: flex;
    justify-content: center;
    gap: 2rem;
    flex-wrap: wrap;
}

.meta-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: var(--light-gray);
    font-size: 0.9rem;
}

.meta-item i {
    color: var(--primary-red);
}

.article-featured-image {
    margin: 2rem 0;
    text-align: center;
}

.featured-img {
    max-width: 100%;
    height: auto;
    border-radius: 15px;
    box-shadow: var(--shadow-luxury);
}

.article-content {
    color: var(--light-gray);
    line-height: 1.8;
    font-size: 1.1rem;
}

.article-content h2,
.article-content h3,
.article-content h4 {
    color: var(--white);
    margin: 2rem 0 1rem;
}

.article-content p {
    margin-bottom: 1.5rem;
}

.article-share {
    margin: 3rem 0;
    padding: 2rem 0;
    border-top: 1px solid rgba(107, 114, 128, 0.2);
    text-align: center;
}

.article-share h4 {
    color: var(--white);
    margin-bottom: 1rem;
}

.share-buttons {
    display: flex;
    justify-content: center;
    gap: 1rem;
    flex-wrap: wrap;
}

.share-btn {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem 1.5rem;
    border-radius: 50px;
    text-decoration: none;
    color: var(--white);
    transition: all 0.3s ease;
    font-size: 0.9rem;
}

.share-btn.facebook { background: #1877f2; }
.share-btn.twitter { background: #1da1f2; }
.share-btn.linkedin { background: #0077b5; }
.share-btn.whatsapp { background: #25d366; }

.share-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
}

.post-navigation {
    margin-top: 3rem;
    padding-top: 2rem;
    border-top: 1px solid rgba(107, 114, 128, 0.2);
}

.nav-links {
    display: flex;
    justify-content: space-between;
    gap: 2rem;
}

.nav-link {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem;
    background: rgba(17, 24, 39, 0.8);
    border-radius: 10px;
    text-decoration: none;
    color: var(--white);
    transition: all 0.3s ease;
    flex: 1;
    max-width: 45%;
}

.nav-link:hover {
    background: rgba(220, 38, 38, 0.1);
    transform: translateY(-2px);
}

.nav-content {
    display: flex;
    flex-direction: column;
}

.nav-label {
    font-size: 0.8rem;
    color: var(--light-gray);
    margin-bottom: 0.25rem;
}

.nav-title {
    font-weight: 500;
    line-height: 1.3;
}

@media (max-width: 768px) {
    .single-article {
        padding: 2rem 1rem;
        margin: 0 1rem;
    }
    
    .article-title {
        font-size: 2rem;
    }
    
    .article-meta {
        flex-direction: column;
        gap: 0.5rem;
    }
    
    .nav-links {
        flex-direction: column;
    }
    
    .nav-link {
        max-width: 100%;
    }
    
    .share-buttons {
        flex-direction: column;
        align-items: center;
    }
}
</style>

<?php get_footer(); ?>
