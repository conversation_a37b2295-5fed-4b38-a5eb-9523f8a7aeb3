/*
Theme Name: Arab Studio
Description: ثيم فخم وعصري لشركة عرب استوديو للإعلان - تصميم داكن مع أنيميشن ثلاثي الأبعاد
Author: Arab Studio
Version: 1.0
Text Domain: arabstudio
*/

/* ===== متغيرات CSS ===== */
:root {
    --primary-red: #dc2626;
    --dark-gray: #1f2937;
    --darker-gray: #111827;
    --light-gray: #6b7280;
    --white: #ffffff;
    --black: #000000;
    --gradient-primary: linear-gradient(135deg, var(--primary-red) 0%, var(--dark-gray) 100%);
    --gradient-dark: linear-gradient(135deg, var(--darker-gray) 0%, var(--dark-gray) 100%);
    --shadow-luxury: 0 25px 50px -12px rgba(0, 0, 0, 0.8);
    --shadow-glow: 0 0 30px rgba(220, 38, 38, 0.3);
}

/* ===== إعادة تعيين الأساسيات ===== */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    scroll-behavior: smooth;
    font-size: 16px;
}

body {
    font-family: 'Cairo', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: var(--gradient-dark);
    color: var(--white);
    line-height: 1.6;
    overflow-x: hidden;
    direction: rtl;
}

/* ===== تأثيرات الخلفية المتحركة ===== */
.animated-bg {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: -1;
    background: var(--gradient-dark);
}

.animated-bg::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: radial-gradient(circle at 20% 50%, rgba(220, 38, 38, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 80% 20%, rgba(220, 38, 38, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 40% 80%, rgba(220, 38, 38, 0.1) 0%, transparent 50%);
    animation: backgroundMove 20s ease-in-out infinite;
}

@keyframes backgroundMove {
    0%, 100% { transform: translateX(0) translateY(0); }
    25% { transform: translateX(-20px) translateY(-10px); }
    50% { transform: translateX(20px) translateY(10px); }
    75% { transform: translateX(-10px) translateY(20px); }
}

/* ===== الهيدر ===== */
.site-header {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    background: rgba(17, 24, 39, 0.95);
    backdrop-filter: blur(20px);
    border-bottom: 1px solid rgba(220, 38, 38, 0.2);
    z-index: 1000;
    transition: all 0.3s ease;
}

.site-header.scrolled {
    background: rgba(17, 24, 39, 0.98);
    box-shadow: var(--shadow-luxury);
}

.header-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 1rem 2rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.logo {
    font-size: 2rem;
    font-weight: bold;
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    text-decoration: none;
    transition: all 0.3s ease;
}

.logo:hover {
    transform: scale(1.05);
    filter: drop-shadow(var(--shadow-glow));
}

/* ===== القائمة الرئيسية ===== */
.main-nav ul {
    display: flex;
    list-style: none;
    gap: 2rem;
}

.main-nav a {
    color: var(--white);
    text-decoration: none;
    font-weight: 500;
    padding: 0.5rem 1rem;
    border-radius: 8px;
    transition: all 0.3s ease;
    position: relative;
}

.main-nav a::before {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    width: 0;
    height: 2px;
    background: var(--primary-red);
    transition: all 0.3s ease;
    transform: translateX(-50%);
}

.main-nav a:hover::before {
    width: 100%;
}

.main-nav a:hover {
    color: var(--primary-red);
    transform: translateY(-2px);
}

/* ===== المحتوى الرئيسي ===== */
.main-content {
    min-height: 100vh;
    padding-top: 80px;
}

/* ===== قسم البطل (Hero Section) ===== */
.hero-section {
    height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    text-align: center;
    position: relative;
    overflow: hidden;
}

.hero-content {
    max-width: 800px;
    z-index: 2;
    animation: fadeInUp 1s ease-out;
}

.hero-title {
    font-size: 4rem;
    font-weight: bold;
    margin-bottom: 1rem;
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    animation: titleGlow 3s ease-in-out infinite alternate;
}

@keyframes titleGlow {
    from { filter: drop-shadow(0 0 10px rgba(220, 38, 38, 0.3)); }
    to { filter: drop-shadow(0 0 30px rgba(220, 38, 38, 0.6)); }
}

.hero-subtitle {
    font-size: 1.5rem;
    color: var(--light-gray);
    margin-bottom: 2rem;
    animation: fadeInUp 1s ease-out 0.3s both;
}

.hero-description {
    font-size: 1.1rem;
    color: var(--light-gray);
    margin-bottom: 3rem;
    animation: fadeInUp 1s ease-out 0.6s both;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* ===== الأزرار ===== */
.btn {
    display: inline-block;
    padding: 1rem 2rem;
    background: var(--gradient-primary);
    color: var(--white);
    text-decoration: none;
    border-radius: 50px;
    font-weight: bold;
    transition: all 0.3s ease;
    border: none;
    cursor: pointer;
    position: relative;
    overflow: hidden;
}

.btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.5s;
}

.btn:hover::before {
    left: 100%;
}

.btn:hover {
    transform: translateY(-3px);
    box-shadow: var(--shadow-glow);
}

/* ===== الفوتر ===== */
.site-footer {
    background: var(--darker-gray);
    padding: 3rem 0 1rem;
    border-top: 1px solid rgba(220, 38, 38, 0.2);
}

.footer-content {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 2rem;
    text-align: center;
}

.company-info h3 {
    color: var(--primary-red);
    font-size: 1.5rem;
    margin-bottom: 1rem;
}

.company-info p {
    color: var(--light-gray);
    margin-bottom: 0.5rem;
}

.footer-bottom {
    margin-top: 2rem;
    padding-top: 2rem;
    border-top: 1px solid rgba(107, 114, 128, 0.2);
    color: var(--light-gray);
}

/* ===== الروابط الاجتماعية ===== */
.social-links {
    display: flex;
    gap: 1rem;
    margin-top: 1.5rem;
}

.social-link {
    width: 45px;
    height: 45px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--white);
    text-decoration: none;
    transition: all 0.3s ease;
    font-size: 1.2rem;
}

.social-link.facebook { background: #1877f2; }
.social-link.instagram { background: linear-gradient(45deg, #f09433 0%, #e6683c 25%, #dc2743 50%, #cc2366 75%, #bc1888 100%); }
.social-link.linkedin { background: #0077b5; }
.social-link.twitter { background: #1da1f2; }
.social-link.youtube { background: #ff0000; }

.social-link:hover {
    transform: translateY(-3px) scale(1.1);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
}

/* ===== تأثيرات الكانفاس ===== */
.hero-canvas,
.about-canvas {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 1;
}

.hero-canvas {
    opacity: 0.7;
}

/* ===== تأثيرات AOS (Animate On Scroll) ===== */
[data-aos] {
    opacity: 0;
    transition-property: opacity, transform;
    transition-duration: 0.6s;
    transition-timing-function: ease-out;
}

[data-aos].aos-animate {
    opacity: 1;
    transform: none;
}

[data-aos="fade-up"] {
    transform: translateY(30px);
}

[data-aos="fade-right"] {
    transform: translateX(-30px);
}

[data-aos="fade-left"] {
    transform: translateX(30px);
}

/* ===== تجاوب الشاشات ===== */
@media (max-width: 1024px) {
    .about-content,
    .contact-content {
        grid-template-columns: 1fr;
        gap: 3rem;
    }

    .about-text {
        padding-right: 0;
    }

    .contact-info {
        padding-right: 0;
    }

    .newsletter-content {
        flex-direction: column;
        text-align: center;
    }

    .newsletter-form .form-group {
        flex-direction: column;
    }
}

@media (max-width: 768px) {
    .hero-title {
        font-size: 2.5rem;
    }

    .hero-subtitle {
        font-size: 1.2rem;
    }

    .section-title {
        font-size: 2.5rem;
    }

    .main-nav {
        display: none;
    }

    .menu-toggle {
        display: flex;
    }

    .header-container {
        padding: 1rem;
    }

    .services-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }

    .service-card {
        padding: 2rem;
    }

    .company-stats {
        flex-direction: column;
        gap: 1.5rem;
    }

    .hero-buttons {
        flex-direction: column;
        align-items: center;
    }

    .btn {
        width: 100%;
        max-width: 300px;
    }

    .whatsapp-float {
        bottom: 5rem;
    }

    .cookie-content {
        flex-direction: column;
        text-align: center;
        gap: 1rem;
    }

    .footer-content {
        grid-template-columns: 1fr;
        gap: 2rem;
        text-align: center;
    }

    .footer-bottom-content {
        flex-direction: column;
        gap: 1rem;
        text-align: center;
    }
}

@media (max-width: 480px) {
    .hero-title {
        font-size: 2rem;
    }

    .section-title {
        font-size: 2rem;
    }

    .container {
        padding: 0 1rem;
    }

    .service-card {
        padding: 1.5rem;
    }

    .contact-form {
        padding: 1.5rem;
    }

    .stat-number {
        font-size: 2.5rem;
    }

    .whatsapp-float a {
        padding: 0.75rem;
        border-radius: 50%;
    }

    .whatsapp-text {
        display: none;
    }
}

/* ===== تحسينات إضافية للأداء ===== */
@media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}

/* ===== طباعة ===== */
@media print {
    .site-header,
    .back-to-top,
    .whatsapp-float,
    .cookie-notice,
    .loading-screen {
        display: none !important;
    }

    body {
        background: white !important;
        color: black !important;
    }

    .hero-section {
        height: auto !important;
        padding: 2rem 0 !important;
    }
}

/* ===== شاشة التحميل ===== */
.loading-screen {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: var(--gradient-dark);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
    transition: opacity 0.5s ease, visibility 0.5s ease;
}

.loading-screen.hidden {
    opacity: 0;
    visibility: hidden;
}

.loading-content {
    text-align: center;
    animation: loadingPulse 2s ease-in-out infinite;
}

.loading-logo h1 {
    font-size: 3rem;
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin-bottom: 2rem;
}

.loading-spinner {
    margin: 2rem 0;
}

.spinner {
    width: 50px;
    height: 50px;
    border: 3px solid rgba(220, 38, 38, 0.3);
    border-top: 3px solid var(--primary-red);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto;
}

.loading-text {
    color: var(--light-gray);
    font-size: 1.1rem;
}

@keyframes loadingPulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.05); }
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* ===== قسم الخدمات ===== */
.services-section {
    padding: 5rem 0;
    background: rgba(17, 24, 39, 0.5);
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 2rem;
}

.section-header {
    text-align: center;
    margin-bottom: 4rem;
}

.section-title {
    font-size: 3rem;
    font-weight: bold;
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin-bottom: 1rem;
}

.section-subtitle {
    font-size: 1.2rem;
    color: var(--light-gray);
    max-width: 600px;
    margin: 0 auto;
}

.services-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 2rem;
    margin-top: 3rem;
}

.service-card {
    background: rgba(31, 41, 55, 0.8);
    padding: 2.5rem;
    border-radius: 20px;
    text-align: center;
    transition: all 0.3s ease;
    border: 1px solid rgba(220, 38, 38, 0.1);
    position: relative;
    overflow: hidden;
}

.service-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: var(--gradient-primary);
    opacity: 0;
    transition: opacity 0.3s ease;
    z-index: -1;
}

.service-card:hover::before {
    opacity: 0.1;
}

.service-card:hover {
    transform: translateY(-10px);
    box-shadow: var(--shadow-luxury);
    border-color: var(--primary-red);
}

.service-icon {
    width: 80px;
    height: 80px;
    background: var(--gradient-primary);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1.5rem;
    font-size: 2rem;
    color: var(--white);
    transition: all 0.3s ease;
}

.service-card:hover .service-icon {
    transform: scale(1.1) rotate(360deg);
}

.service-title {
    font-size: 1.5rem;
    font-weight: bold;
    color: var(--white);
    margin-bottom: 1rem;
}

.service-description {
    color: var(--light-gray);
    line-height: 1.6;
}

/* ===== قسم عن الشركة ===== */
.about-section {
    padding: 5rem 0;
    background: var(--darker-gray);
}

.about-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: center;
}

.about-text {
    padding-right: 2rem;
}

.about-description {
    color: var(--light-gray);
    font-size: 1.1rem;
    line-height: 1.8;
    margin-bottom: 1.5rem;
}

.company-stats {
    display: flex;
    gap: 2rem;
    margin-top: 3rem;
}

.stat-item {
    text-align: center;
}

.stat-number {
    display: block;
    font-size: 3rem;
    font-weight: bold;
    color: var(--primary-red);
    margin-bottom: 0.5rem;
}

.stat-label {
    color: var(--light-gray);
    font-size: 0.9rem;
}

.about-visual {
    position: relative;
    height: 400px;
}

.about-canvas {
    width: 100%;
    height: 100%;
    border-radius: 20px;
}

/* ===== قسم التواصل ===== */
.contact-section {
    padding: 5rem 0;
    background: rgba(17, 24, 39, 0.5);
}

.contact-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    margin-top: 3rem;
}

.contact-info {
    padding-right: 2rem;
}

.contact-item {
    display: flex;
    align-items: flex-start;
    gap: 1rem;
    margin-bottom: 2rem;
}

.contact-icon {
    width: 50px;
    height: 50px;
    background: var(--gradient-primary);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--white);
    font-size: 1.2rem;
    flex-shrink: 0;
}

.contact-details h4 {
    color: var(--white);
    font-size: 1.2rem;
    margin-bottom: 0.5rem;
}

.contact-details p {
    color: var(--light-gray);
    line-height: 1.6;
}

.contact-form {
    background: rgba(31, 41, 55, 0.8);
    padding: 2.5rem;
    border-radius: 20px;
    border: 1px solid rgba(220, 38, 38, 0.1);
}

.form-group {
    margin-bottom: 1.5rem;
}

.form-group input,
.form-group select,
.form-group textarea {
    width: 100%;
    padding: 1rem;
    background: rgba(17, 24, 39, 0.8);
    border: 1px solid rgba(107, 114, 128, 0.3);
    border-radius: 10px;
    color: var(--white);
    font-size: 1rem;
    transition: all 0.3s ease;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: var(--primary-red);
    box-shadow: 0 0 0 3px rgba(220, 38, 38, 0.1);
}

.form-group input::placeholder,
.form-group textarea::placeholder {
    color: var(--light-gray);
}

/* ===== القائمة المحمولة ===== */
.menu-toggle {
    display: none;
    background: none;
    border: none;
    color: var(--white);
    font-size: 1.5rem;
    cursor: pointer;
    padding: 0.5rem;
    align-items: center;
    gap: 0.5rem;
}

.hamburger {
    display: flex;
    flex-direction: column;
    gap: 3px;
}

.hamburger-line {
    width: 25px;
    height: 3px;
    background: var(--white);
    transition: all 0.3s ease;
}

.menu-toggle.active .hamburger-line:nth-child(1) {
    transform: rotate(45deg) translate(6px, 6px);
}

.menu-toggle.active .hamburger-line:nth-child(2) {
    opacity: 0;
}

.menu-toggle.active .hamburger-line:nth-child(3) {
    transform: rotate(-45deg) translate(6px, -6px);
}

.mobile-menu-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(17, 24, 39, 0.98);
    backdrop-filter: blur(20px);
    z-index: 9998;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
}

.mobile-menu-overlay.active {
    opacity: 1;
    visibility: visible;
}

.mobile-menu-content {
    padding: 2rem;
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    text-align: center;
}

.mobile-menu-close {
    position: absolute;
    top: 2rem;
    right: 2rem;
    background: none;
    border: none;
    color: var(--white);
    font-size: 2rem;
    cursor: pointer;
}

.mobile-nav-menu {
    list-style: none;
    margin: 2rem 0;
}

.mobile-nav-menu li {
    margin: 1rem 0;
}

.mobile-nav-menu a {
    color: var(--white);
    text-decoration: none;
    font-size: 1.5rem;
    font-weight: 500;
    transition: all 0.3s ease;
}

.mobile-nav-menu a:hover {
    color: var(--primary-red);
}

.mobile-menu-footer {
    margin-top: 2rem;
}

.mobile-contact-info p {
    color: var(--light-gray);
    margin: 0.5rem 0;
    font-size: 0.9rem;
}

.mobile-social-links {
    display: flex;
    gap: 1rem;
    justify-content: center;
    margin-top: 1rem;
}

.mobile-social-links a {
    width: 40px;
    height: 40px;
    background: var(--gradient-primary);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--white);
    text-decoration: none;
    transition: all 0.3s ease;
}

.mobile-social-links a:hover {
    transform: scale(1.1);
}

/* ===== أزرار إضافية ===== */
.btn-secondary {
    background: transparent;
    border: 2px solid var(--primary-red);
    color: var(--primary-red);
}

.btn-secondary:hover {
    background: var(--primary-red);
    color: var(--white);
}

.btn-outline {
    background: transparent;
    border: 1px solid rgba(255, 255, 255, 0.3);
    color: var(--white);
    padding: 0.75rem 1.5rem;
    font-size: 0.9rem;
}

.btn-outline:hover {
    background: var(--white);
    color: var(--dark-gray);
}

.hero-buttons {
    display: flex;
    gap: 1rem;
    justify-content: center;
    margin-top: 2rem;
    animation: fadeInUp 1s ease-out 0.9s both;
}

/* ===== العودة إلى الأعلى ===== */
.back-to-top {
    position: fixed;
    bottom: 2rem;
    right: 2rem;
    width: 50px;
    height: 50px;
    background: var(--gradient-primary);
    border: none;
    border-radius: 50%;
    color: var(--white);
    font-size: 1.2rem;
    cursor: pointer;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
    z-index: 1000;
}

.back-to-top.visible {
    opacity: 1;
    visibility: visible;
}

.back-to-top:hover {
    transform: translateY(-3px);
    box-shadow: var(--shadow-glow);
}

/* ===== واتساب العائم ===== */
.whatsapp-float {
    position: fixed;
    bottom: 2rem;
    left: 2rem;
    z-index: 1000;
}

.whatsapp-float a {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    background: #25d366;
    color: var(--white);
    padding: 1rem 1.5rem;
    border-radius: 50px;
    text-decoration: none;
    font-weight: 500;
    transition: all 0.3s ease;
    box-shadow: 0 4px 20px rgba(37, 211, 102, 0.3);
}

.whatsapp-float a:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 30px rgba(37, 211, 102, 0.4);
}

.whatsapp-float i {
    font-size: 1.5rem;
}

/* ===== إشعار الكوكيز ===== */
.cookie-notice {
    position: fixed;
    bottom: 0;
    left: 0;
    width: 100%;
    background: rgba(17, 24, 39, 0.98);
    backdrop-filter: blur(20px);
    border-top: 1px solid rgba(220, 38, 38, 0.2);
    padding: 1rem 0;
    z-index: 9999;
    transform: translateY(100%);
    transition: transform 0.3s ease;
}

.cookie-notice.visible {
    transform: translateY(0);
}

.cookie-content {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 2rem;
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 2rem;
}

.cookie-text p {
    color: var(--light-gray);
    margin: 0;
}

.cookie-actions {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.cookie-link {
    color: var(--primary-red);
    text-decoration: none;
    font-size: 0.9rem;
}

.cookie-link:hover {
    text-decoration: underline;
}

/* ===== النشرة الإخبارية ===== */
.newsletter-section {
    background: rgba(31, 41, 55, 0.8);
    padding: 3rem 0;
    border-top: 1px solid rgba(220, 38, 38, 0.2);
    border-bottom: 1px solid rgba(220, 38, 38, 0.2);
}

.newsletter-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 2rem;
}

.newsletter-text h4 {
    color: var(--white);
    font-size: 1.5rem;
    margin-bottom: 0.5rem;
}

.newsletter-text p {
    color: var(--light-gray);
    margin: 0;
}

.newsletter-form .form-group {
    display: flex;
    gap: 1rem;
    margin: 0;
}

.newsletter-form input {
    flex: 1;
    padding: 1rem;
    background: rgba(17, 24, 39, 0.8);
    border: 1px solid rgba(107, 114, 128, 0.3);
    border-radius: 50px;
    color: var(--white);
    font-size: 1rem;
}

.newsletter-form input::placeholder {
    color: var(--light-gray);
}

.newsletter-form button {
    padding: 1rem 2rem;
    border-radius: 50px;
    white-space: nowrap;
}

/* ===== المؤشر المخصص ===== */
.custom-cursor {
    position: fixed;
    width: 10px;
    height: 10px;
    background: var(--primary-red);
    border-radius: 50%;
    pointer-events: none;
    z-index: 9999;
    transition: transform 0.1s ease;
    mix-blend-mode: difference;
}

.cursor-follower {
    position: fixed;
    width: 40px;
    height: 40px;
    border: 2px solid var(--primary-red);
    border-radius: 50%;
    pointer-events: none;
    z-index: 9998;
    transition: transform 0.3s ease;
    opacity: 0.5;
}

.custom-cursor.cursor-hover {
    transform: scale(2);
    background: var(--white);
}

.cursor-follower.cursor-hover {
    transform: scale(1.5);
    border-color: var(--white);
    opacity: 0.8;
}

/* إخفاء المؤشر المخصص على الأجهزة اللمسية */
@media (hover: none) {
    .custom-cursor,
    .cursor-follower {
        display: none;
    }
}

/* ===== تأثيرات الجسيمات المتقدمة ===== */
.particle-container {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    overflow: hidden;
    pointer-events: none;
}

.particle {
    position: absolute;
    width: 4px;
    height: 4px;
    background: var(--primary-red);
    border-radius: 50%;
    opacity: 0.6;
    animation: floatParticle 10s infinite linear;
}

.particle:nth-child(2n) {
    background: var(--light-gray);
    animation-duration: 15s;
}

.particle:nth-child(3n) {
    background: var(--white);
    animation-duration: 12s;
}

@keyframes floatParticle {
    0% {
        transform: translateY(100vh) translateX(0) rotate(0deg);
        opacity: 0;
    }
    10% {
        opacity: 0.6;
    }
    90% {
        opacity: 0.6;
    }
    100% {
        transform: translateY(-100px) translateX(100px) rotate(360deg);
        opacity: 0;
    }
}

/* ===== تأثيرات التمرير المتوازي ===== */
.parallax-element {
    transform: translateZ(0);
    will-change: transform;
}

/* ===== تأثيرات الإضاءة ===== */
.glow-effect {
    position: relative;
}

.glow-effect::before {
    content: '';
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;
    background: linear-gradient(45deg, var(--primary-red), transparent, var(--primary-red));
    border-radius: inherit;
    z-index: -1;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.glow-effect:hover::before {
    opacity: 0.7;
    animation: glowRotate 2s linear infinite;
}

@keyframes glowRotate {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* ===== تأثيرات الموجات ===== */
.wave-effect {
    position: relative;
    overflow: hidden;
}

.wave-effect::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    background: rgba(220, 38, 38, 0.3);
    border-radius: 50%;
    transform: translate(-50%, -50%);
    transition: width 0.6s ease, height 0.6s ease;
}

.wave-effect:hover::after {
    width: 300px;
    height: 300px;
}

/* ===== تأثيرات النيون ===== */
.neon-text {
    color: var(--white);
    text-shadow:
        0 0 5px var(--primary-red),
        0 0 10px var(--primary-red),
        0 0 15px var(--primary-red),
        0 0 20px var(--primary-red);
    animation: neonFlicker 2s infinite alternate;
}

@keyframes neonFlicker {
    0%, 100% {
        text-shadow:
            0 0 5px var(--primary-red),
            0 0 10px var(--primary-red),
            0 0 15px var(--primary-red),
            0 0 20px var(--primary-red);
    }
    50% {
        text-shadow:
            0 0 2px var(--primary-red),
            0 0 5px var(--primary-red),
            0 0 8px var(--primary-red),
            0 0 12px var(--primary-red);
    }
}

/* ===== تأثيرات الهولوجرام ===== */
.hologram-effect {
    position: relative;
    background: linear-gradient(45deg, transparent 30%, rgba(220, 38, 38, 0.1) 50%, transparent 70%);
    background-size: 20px 20px;
    animation: hologramMove 3s linear infinite;
}

@keyframes hologramMove {
    0% { background-position: 0 0; }
    100% { background-position: 20px 20px; }
}

/* ===== تأثيرات الزجاج المصقول ===== */
.glass-effect {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
}

/* ===== تأثيرات الأداء المنخفض ===== */
.low-performance * {
    animation-duration: 0.1s !important;
    transition-duration: 0.1s !important;
}

.low-performance .particle,
.low-performance .animated-bg::before {
    display: none;
}

/* ===== تأثيرات الصور الكسولة ===== */
img.lazy {
    opacity: 0;
    transition: opacity 0.3s ease;
}

img.lazy.loaded {
    opacity: 1;
}

/* ===== تأثيرات التحميل المتقدمة ===== */
.loading-dots {
    display: inline-block;
}

.loading-dots::after {
    content: '';
    animation: loadingDots 1.5s infinite;
}

@keyframes loadingDots {
    0%, 20% { content: ''; }
    40% { content: '.'; }
    60% { content: '..'; }
    80%, 100% { content: '...'; }
}

/* ===== تحسينات الأداء ===== */
.will-change {
    will-change: transform;
}

.gpu-accelerated {
    transform: translateZ(0);
    backface-visibility: hidden;
    perspective: 1000px;
}

/* ===== تأثيرات الحركة المتقدمة ===== */
@keyframes morphing {
    0%, 100% { border-radius: 50% 50% 50% 50%; }
    25% { border-radius: 60% 40% 60% 40%; }
    50% { border-radius: 40% 60% 40% 60%; }
    75% { border-radius: 50% 50% 60% 40%; }
}

.morphing-shape {
    animation: morphing 4s ease-in-out infinite;
}

/* ===== تأثيرات الانعكاس ===== */
.reflection-effect {
    position: relative;
}

.reflection-effect::after {
    content: '';
    position: absolute;
    top: 100%;
    left: 0;
    width: 100%;
    height: 50%;
    background: linear-gradient(to bottom, rgba(255,255,255,0.1), transparent);
    transform: scaleY(-1);
    opacity: 0.3;
    pointer-events: none;
}
