<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار التجاوب - عرب استوديو</title>
    <link rel="stylesheet" href="style.css">
    <link rel="stylesheet" href="responsive.css">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700;800;900&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        /* أنماط اختبار إضافية */
        .test-info {
            position: fixed;
            top: 10px;
            left: 10px;
            background: rgba(220, 38, 38, 0.9);
            color: white;
            padding: 10px;
            border-radius: 5px;
            font-size: 12px;
            z-index: 10000;
            font-family: monospace;
        }
        
        .test-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin: 2rem 0;
        }
        
        .test-card {
            background: rgba(31, 41, 55, 0.8);
            padding: 1rem;
            border-radius: 10px;
            border: 1px solid rgba(220, 38, 38, 0.2);
            text-align: center;
        }
        
        .breakpoint-indicator {
            position: fixed;
            bottom: 10px;
            left: 10px;
            background: rgba(17, 24, 39, 0.9);
            color: white;
            padding: 5px 10px;
            border-radius: 3px;
            font-size: 11px;
            z-index: 10000;
        }
        
        .breakpoint-indicator::before {
            content: 'XS';
        }
        
        @media (min-width: 576px) {
            .breakpoint-indicator::before {
                content: 'SM';
            }
        }
        
        @media (min-width: 768px) {
            .breakpoint-indicator::before {
                content: 'MD';
            }
        }
        
        @media (min-width: 992px) {
            .breakpoint-indicator::before {
                content: 'LG';
            }
        }
        
        @media (min-width: 1200px) {
            .breakpoint-indicator::before {
                content: 'XL';
            }
        }
        
        @media (min-width: 1400px) {
            .breakpoint-indicator::before {
                content: 'XXL';
            }
        }
    </style>
</head>
<body>
    <!-- مؤشر معلومات الاختبار -->
    <div class="test-info" id="testInfo">
        العرض: <span id="screenWidth"></span>px<br>
        الارتفاع: <span id="screenHeight"></span>px<br>
        نسبة البكسل: <span id="pixelRatio"></span><br>
        الجهاز: <span id="deviceType"></span>
    </div>
    
    <!-- مؤشر نقطة الكسر -->
    <div class="breakpoint-indicator"></div>
    
    <!-- الخلفية المتحركة -->
    <div class="animated-bg"></div>
    
    <!-- الهيدر -->
    <header class="site-header">
        <div class="header-container">
            <div class="site-branding">
                <a href="#" class="logo">عرب استوديو</a>
            </div>
            
            <nav class="main-nav">
                <ul>
                    <li><a href="#home">الرئيسية</a></li>
                    <li><a href="#services">الخدمات</a></li>
                    <li><a href="#about">عن الشركة</a></li>
                    <li><a href="#contact">تواصل معنا</a></li>
                </ul>
            </nav>
            
            <button class="menu-toggle">
                <span class="hamburger">
                    <span class="hamburger-line"></span>
                    <span class="hamburger-line"></span>
                    <span class="hamburger-line"></span>
                </span>
                <span class="menu-text">القائمة</span>
            </button>
        </div>
    </header>
    
    <!-- المحتوى الرئيسي -->
    <main class="main-content">
        <!-- قسم البطل -->
        <section class="hero-section" id="home">
            <div class="hero-content">
                <h1 class="hero-title">عرب استوديو</h1>
                <h2 class="hero-subtitle">شركة الإعلان والتسويق الرقمي</h2>
                <p class="hero-description">
                    اختبار التجاوب لجميع الأجهزة والشاشات. هذا النص يختبر كيفية ظهور المحتوى على الأحجام المختلفة.
                </p>
                <div class="hero-buttons">
                    <a href="#services" class="btn btn-primary">خدماتنا</a>
                    <a href="#contact" class="btn btn-secondary">تواصل معنا</a>
                </div>
            </div>
            <canvas id="hero-canvas" class="hero-canvas"></canvas>
        </section>
        
        <!-- قسم اختبار الشبكة -->
        <section class="services-section">
            <div class="container">
                <div class="section-header">
                    <h2 class="section-title">اختبار الشبكة المتجاوبة</h2>
                    <p class="section-subtitle">هذا القسم يختبر كيفية تجاوب الشبكة مع الأحجام المختلفة</p>
                </div>
                
                <div class="test-grid">
                    <div class="test-card">
                        <div class="service-icon">
                            <i class="fas fa-mobile-alt"></i>
                        </div>
                        <h3>الهواتف الذكية</h3>
                        <p>اختبار العرض على الهواتف الذكية بأحجام مختلفة</p>
                    </div>
                    
                    <div class="test-card">
                        <div class="service-icon">
                            <i class="fas fa-tablet-alt"></i>
                        </div>
                        <h3>الأجهزة اللوحية</h3>
                        <p>اختبار العرض على الأجهزة اللوحية</p>
                    </div>
                    
                    <div class="test-card">
                        <div class="service-icon">
                            <i class="fas fa-laptop"></i>
                        </div>
                        <h3>أجهزة الكمبيوتر</h3>
                        <p>اختبار العرض على أجهزة الكمبيوتر المحمولة</p>
                    </div>
                    
                    <div class="test-card">
                        <div class="service-icon">
                            <i class="fas fa-desktop"></i>
                        </div>
                        <h3>الشاشات الكبيرة</h3>
                        <p>اختبار العرض على الشاشات الكبيرة</p>
                    </div>
                    
                    <div class="test-card">
                        <div class="service-icon">
                            <i class="fas fa-tv"></i>
                        </div>
                        <h3>الشاشات العملاقة</h3>
                        <p>اختبار العرض على الشاشات العملاقة</p>
                    </div>
                    
                    <div class="test-card">
                        <div class="service-icon">
                            <i class="fas fa-watch"></i>
                        </div>
                        <h3>الساعات الذكية</h3>
                        <p>اختبار العرض على الساعات الذكية</p>
                    </div>
                </div>
            </div>
        </section>
        
        <!-- قسم اختبار النماذج -->
        <section class="contact-section">
            <div class="container">
                <div class="section-header">
                    <h2 class="section-title">اختبار النماذج المتجاوبة</h2>
                </div>
                
                <div class="contact-content">
                    <div class="contact-info">
                        <div class="contact-item">
                            <div class="contact-icon">
                                <i class="fas fa-info-circle"></i>
                            </div>
                            <div class="contact-details">
                                <h4>معلومات الاختبار</h4>
                                <p>هذا القسم يختبر تجاوب النماذج والمعلومات على الأجهزة المختلفة</p>
                            </div>
                        </div>
                    </div>
                    
                    <div class="contact-form">
                        <h3>نموذج اختبار التجاوب</h3>
                        <form>
                            <div class="form-group">
                                <input type="text" placeholder="الاسم الكامل" required>
                            </div>
                            <div class="form-group">
                                <input type="email" placeholder="البريد الإلكتروني" required>
                            </div>
                            <div class="form-group">
                                <select required>
                                    <option value="">اختر نوع الجهاز</option>
                                    <option value="mobile">هاتف ذكي</option>
                                    <option value="tablet">جهاز لوحي</option>
                                    <option value="laptop">كمبيوتر محمول</option>
                                    <option value="desktop">كمبيوتر مكتبي</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <textarea placeholder="ملاحظات حول التجاوب" rows="4"></textarea>
                            </div>
                            <button type="submit" class="btn btn-primary">إرسال الاختبار</button>
                        </form>
                    </div>
                </div>
            </div>
        </section>
    </main>
    
    <!-- الفوتر -->
    <footer class="site-footer">
        <div class="footer-main">
            <div class="container">
                <div class="footer-content">
                    <div class="footer-section">
                        <h3>عرب استوديو</h3>
                        <p>اختبار تجاوب الفوتر على جميع الأجهزة</p>
                        <div class="social-links">
                            <a href="#" class="social-link facebook"><i class="fab fa-facebook-f"></i></a>
                            <a href="#" class="social-link instagram"><i class="fab fa-instagram"></i></a>
                            <a href="#" class="social-link linkedin"><i class="fab fa-linkedin-in"></i></a>
                        </div>
                    </div>
                    
                    <div class="footer-section">
                        <h4>روابط سريعة</h4>
                        <ul>
                            <li><a href="#">الرئيسية</a></li>
                            <li><a href="#">الخدمات</a></li>
                            <li><a href="#">عن الشركة</a></li>
                            <li><a href="#">تواصل معنا</a></li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="footer-bottom">
            <div class="container">
                <div class="footer-bottom-content">
                    <div class="copyright">
                        <p>&copy; 2025 عرب استوديو. جميع الحقوق محفوظة.</p>
                    </div>
                </div>
            </div>
        </div>
    </footer>
    
    <!-- أزرار التحكم -->
    <button class="back-to-top">
        <i class="fas fa-chevron-up"></i>
    </button>
    
    <div class="whatsapp-float">
        <a href="#">
            <i class="fab fa-whatsapp"></i>
            <span class="whatsapp-text">واتساب</span>
        </a>
    </div>
    
    <!-- سكريبت الاختبار -->
    <script>
        // تحديث معلومات الشاشة
        function updateScreenInfo() {
            document.getElementById('screenWidth').textContent = window.innerWidth;
            document.getElementById('screenHeight').textContent = window.innerHeight;
            document.getElementById('pixelRatio').textContent = window.devicePixelRatio || 1;
            
            // تحديد نوع الجهاز
            let deviceType = 'غير محدد';
            const width = window.innerWidth;
            
            if (width < 576) {
                deviceType = 'هاتف صغير';
            } else if (width < 768) {
                deviceType = 'هاتف كبير';
            } else if (width < 992) {
                deviceType = 'جهاز لوحي';
            } else if (width < 1200) {
                deviceType = 'كمبيوتر صغير';
            } else if (width < 1400) {
                deviceType = 'كمبيوتر كبير';
            } else {
                deviceType = 'شاشة عملاقة';
            }
            
            document.getElementById('deviceType').textContent = deviceType;
        }
        
        // تحديث المعلومات عند تحميل الصفحة وتغيير الحجم
        updateScreenInfo();
        window.addEventListener('resize', updateScreenInfo);
        
        // اختبار القائمة المحمولة
        document.querySelector('.menu-toggle').addEventListener('click', function() {
            this.classList.toggle('active');
            alert('تم النقر على قائمة الهاتف المحمول');
        });
        
        // اختبار الأزرار
        document.querySelectorAll('.btn').forEach(btn => {
            btn.addEventListener('click', function(e) {
                e.preventDefault();
                alert('تم النقر على الزر: ' + this.textContent.trim());
            });
        });
        
        // اختبار النموذج
        document.querySelector('form').addEventListener('submit', function(e) {
            e.preventDefault();
            alert('تم إرسال النموذج بنجاح! (اختبار)');
        });
        
        // اختبار العودة للأعلى
        document.querySelector('.back-to-top').addEventListener('click', function() {
            window.scrollTo({ top: 0, behavior: 'smooth' });
        });
        
        // إظهار/إخفاء زر العودة للأعلى
        window.addEventListener('scroll', function() {
            const backToTop = document.querySelector('.back-to-top');
            if (window.scrollY > 300) {
                backToTop.classList.add('visible');
            } else {
                backToTop.classList.remove('visible');
            }
        });
        
        // اختبار الأداء
        window.addEventListener('load', function() {
            const loadTime = performance.timing.loadEventEnd - performance.timing.navigationStart;
            console.log('وقت تحميل الصفحة:', loadTime + 'ms');
            
            // إضافة معلومات الأداء
            const testInfo = document.getElementById('testInfo');
            testInfo.innerHTML += '<br>وقت التحميل: ' + loadTime + 'ms';
        });
    </script>
</body>
</html>
